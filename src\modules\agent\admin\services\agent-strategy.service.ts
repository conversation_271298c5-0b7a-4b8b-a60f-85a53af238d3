import { CdnService } from '@/shared/services/cdn.service';
import { S3Service } from '@/shared/services/s3.service';
import { CategoryFolderEnum, FileSizeEnum, generateS3Key, ImageType, TimeIntervalEnum } from '@/shared/utils';
import { AppException } from '@common/exceptions';
import { PaginatedResult } from '@common/response';
import {
  AgentStrategyDetailResponseDto,
  AgentStrategyResponseDto,
  CreateAgentStrategyDto,
  QueryAgentStrategyDto,
  UpdateAgentStrategyDto
} from '@modules/agent/admin/dto/agent-strategy';
import { AgentStrategyMapper } from '@modules/agent/admin/mappers/agent-strategy.mapper';
import { Agent } from '@modules/agent/entities/agent.entity';
import { AgentStrategy } from '@modules/agent/entities/agents-strategy.entity';
import { AGENT_ERROR_CODES } from '@modules/agent/exceptions';
import { AgentRepository, AgentStrategyRepository } from '@modules/agent/repositories';
import { KNOWLEDGE_FILE_ERROR_CODES } from '@modules/data/knowledge-files/exceptions';
import { VectorStoreRepository } from '@modules/data/knowledge-files/repositories';
import { EmployeeInfoService } from '@modules/employee/services/employee-info.service';
import { MODELS_ERROR_CODES } from '@modules/models/exceptions';
import { SystemModelsRepository } from '@modules/models/repositories';
import { Injectable, Logger } from '@nestjs/common';
import { IStrategyContentStep } from '../../interfaces/strategy-content-step.interface';
import { ModelConfig } from '../../interfaces/model-config.interface';

/**
 * Service xử lý logic nghiệp vụ cho AgentStrategy
 */
@Injectable()
export class AgentStrategyAdminService {
  private readonly logger = new Logger(AgentStrategyAdminService.name);

  constructor(
    private readonly agentStrategyRepository: AgentStrategyRepository,
    private readonly agentRepository: AgentRepository,
    private readonly vectorStoreRepository: VectorStoreRepository,
    private readonly systemModelsRepository: SystemModelsRepository,
    private readonly s3Service: S3Service,
    private readonly cdnService: CdnService,
    private readonly employeeInfoService: EmployeeInfoService,
  ) { }

  /**
   * Lấy danh sách chiến lược agent có phân trang
   * @param queryDto Tham số truy vấn
   * @returns Danh sách chiến lược agent có phân trang
   */
  async getStrategies(queryDto: QueryAgentStrategyDto): Promise<PaginatedResult<AgentStrategyResponseDto>> {
    try {
      this.logger.log(`Lấy danh sách chiến lược agent với tham số: ${JSON.stringify(queryDto)}`);

      const result = await this.agentStrategyRepository.findPaginated(queryDto);

      return {
        items: AgentStrategyMapper.toResponseDtoArray(result.items, this.cdnService),
        meta: result.meta,
      };
    } catch (error) {
      this.logger.error(`Lỗi khi lấy danh sách chiến lược agent: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        AGENT_ERROR_CODES.STRATEGY_NOT_FOUND,
        'Không thể lấy danh sách chiến lược agent'
      );
    }
  }

  /**
   * Lấy danh sách chiến lược agent đã xóa có phân trang
   * @param queryDto Tham số truy vấn
   * @returns Danh sách chiến lược agent đã xóa có phân trang
   */
  async getDeletedStrategies(queryDto: QueryAgentStrategyDto): Promise<PaginatedResult<AgentStrategyResponseDto>> {
    try {
      this.logger.log(`Lấy danh sách chiến lược agent đã xóa với tham số: ${JSON.stringify(queryDto)}`);

      const result = await this.agentStrategyRepository.findDeletedPaginated(queryDto);

      return {
        items: AgentStrategyMapper.toResponseDtoArray(result.items, this.cdnService),
        meta: result.meta,
      };
    } catch (error) {
      this.logger.error(`Lỗi khi lấy danh sách chiến lược agent đã xóa: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        AGENT_ERROR_CODES.STRATEGY_NOT_FOUND,
        'Không thể lấy danh sách chiến lược agent đã xóa'
      );
    }
  }

  /**
   * Lấy chi tiết chiến lược agent theo ID
   * @param id ID của chiến lược agent
   * @returns Chi tiết chiến lược agent
   */
  async getStrategyById(id: string): Promise<AgentStrategyDetailResponseDto> {
    try {
      this.logger.log(`Lấy chi tiết chiến lược agent với ID: ${id}`);

      const result = await this.agentStrategyRepository.findById(id);

      if (!result) {
        throw new AppException(
          AGENT_ERROR_CODES.STRATEGY_NOT_FOUND,
          'Không tìm thấy chiến lược agent'
        );
      }

      // Lấy thông tin employee từ EmployeeInfoService
      let createdByEmployee: { employeeId: number; name: string; avatar: string | null } | undefined;
      let updatedByEmployee: { employeeId: number; name: string; avatar: string | null } | undefined;

      if (result.strategy.createdBy) {
        try {
          const createdByInfo = await this.employeeInfoService.getEmployeeInfo(result.strategy.createdBy);
          createdByEmployee = {
            employeeId: createdByInfo.id,
            name: createdByInfo.name,
            avatar: createdByInfo.avatar
          };
        } catch (error) {
          this.logger.warn(`Không thể lấy thông tin employee createdBy ${result.strategy.createdBy}: ${error.message}`);
          createdByEmployee = {
            employeeId: result.strategy.createdBy,
            name: 'Unknown',
            avatar: null
          };
        }
      }

      if (result.strategy.updatedBy) {
        try {
          const updatedByInfo = await this.employeeInfoService.getEmployeeInfo(result.strategy.updatedBy);
          updatedByEmployee = {
            employeeId: updatedByInfo.id,
            name: updatedByInfo.name,
            avatar: updatedByInfo.avatar
          };
        } catch (error) {
          this.logger.warn(`Không thể lấy thông tin employee updatedBy ${result.strategy.updatedBy}: ${error.message}`);
          updatedByEmployee = {
            employeeId: result.strategy.updatedBy,
            name: 'Unknown',
            avatar: null
          };
        }
      }

      return AgentStrategyMapper.toDetailResponseDto(
        result.agent,
        result.strategy,
        this.cdnService,
        result.modelId,
        result.modelSystemId,
        createdByEmployee,
        updatedByEmployee
      );
    } catch (error) {
      this.logger.error(`Lỗi khi lấy chi tiết chiến lược agent ${id}: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        AGENT_ERROR_CODES.STRATEGY_NOT_FOUND,
        'Không thể lấy chi tiết chiến lược agent'
      );
    }
  }

  /**
   * Tạo agent và chiến lược agent mới
   * @param createDto Dữ liệu tạo agent và chiến lược
   * @param createdBy ID nhân viên tạo
   * @returns Chi tiết agent và chiến lược đã tạo
   */
  async createStrategy(createDto: CreateAgentStrategyDto, createdBy: number): Promise<{ id: string; avatarUploadUrl: string | null; }> {
    try {
      this.logger.log(`Tạo agent và chiến lược mới với dữ liệu: ${JSON.stringify(createDto)}`);

      // Validation: Kiểm tra dữ liệu đầu vào
      if (!createDto.name?.trim()) {
        throw new AppException(
          AGENT_ERROR_CODES.STRATEGY_CREATION_FAILED,
          'Tên agent không được để trống'
        );
      }

      if (!createDto.content || createDto.content.length === 0) {
        throw new AppException(
          AGENT_ERROR_CODES.STRATEGY_CREATION_FAILED,
          'Nội dung chiến lược không được để trống'
        );
      }

      if (!createDto.exampleDefault || createDto.exampleDefault.length === 0) {
        throw new AppException(
          AGENT_ERROR_CODES.STRATEGY_CREATION_FAILED,
          'Ví dụ mặc định không được để trống'
        );
      }

      // Validate strategy content structure
      this.validateStrategyContent(createDto.content);
      this.validateStrategyContent(createDto.exampleDefault);

      // Validate model config
      this.validateModelConfig(createDto.modelConfig);

      // Validate vector store exists (nếu có cung cấp)
      if (createDto.vectorStoreId) {
        await this.validateVectorStore(createDto.vectorStoreId);
      }

      // Validate system model exists (bắt buộc)
      await this.validateSystemModel(createDto.systemModelId);

      let avatarUploadUrl: string | null = null;
      let avatarKey: string | null = null;
      if (createDto.avatarMimeType) {
        avatarKey = generateS3Key({
          baseFolder: createdBy.toString(),
          categoryFolder: CategoryFolderEnum.AGENT,
        });

        avatarUploadUrl = await this.s3Service.createPresignedWithID(
          avatarKey,
          TimeIntervalEnum.ONE_HOUR,
          ImageType.getType(createDto.avatarMimeType),
          FileSizeEnum.FIVE_MB,
        );
      }

      // 1. Tạo Agent trước
      const agentData: Partial<Agent> = {
        name: createDto.name,
        avatar: avatarKey,
        modelConfig: createDto.modelConfig,
        instruction: createDto.instruction || null,
        vectorStoreId: createDto.vectorStoreId || null,
        createdAt: Date.now(),
        updatedAt: Date.now(),
      };

      const newAgent = await this.agentRepository.save(agentData);

      try {
        // 2. Tạo AgentStrategy với ID từ Agent
        const strategyData: Partial<AgentStrategy> = {
          id: newAgent.id,
          content: createDto.content,
          exampleDefault: createDto.exampleDefault,
          systemModelId: createDto.systemModelId, // Bắt buộc có
          createdBy,
          updatedBy: createdBy,
        };

        await this.agentStrategyRepository.createStrategy(strategyData);

        this.logger.log(`Đã tạo agent và chiến lược thành công với ID: ${newAgent.id}`);
        return { id: newAgent.id, avatarUploadUrl: avatarUploadUrl };
      } catch (strategyError) {
        // Nếu tạo strategy thất bại, xóa agent đã tạo
        this.logger.error(`Lỗi khi tạo strategy, đang rollback agent ${newAgent.id}`);
        await this.agentRepository.delete(newAgent.id);
        throw strategyError;
      }
    } catch (error) {
      this.logger.error(`Lỗi khi tạo agent và chiến lược: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        AGENT_ERROR_CODES.STRATEGY_CREATION_FAILED,
        'Không thể tạo agent và chiến lược'
      );
    }
  }

  /**
   * Cập nhật chiến lược agent
   * @param id ID của chiến lược agent
   * @param updateDto Dữ liệu cập nhật
   * @param updatedBy ID nhân viên cập nhật
   * @returns Chi tiết chiến lược agent đã cập nhật
   */
  async updateStrategy(id: string, updateDto: UpdateAgentStrategyDto, updatedBy: number): Promise<{ id: string; avatarUploadUrl: string | null; }> {
    try {
      this.logger.log(`Cập nhật chiến lược agent ${id} với dữ liệu: ${JSON.stringify(updateDto)}`);

      // Kiểm tra sự tồn tại của agent và strategy
      const existingResult = await this.agentStrategyRepository.findById(id);
      if (!existingResult) {
        throw new AppException(
          AGENT_ERROR_CODES.STRATEGY_NOT_FOUND,
          'Không tìm thấy agent và chiến lược để cập nhật'
        );
      }

      // Cập nhật Agent nếu có thay đổi
      const agentUpdateData: Partial<Agent> = {};
      let shouldUpdateAgent = false;

      if (updateDto.name !== undefined) {
        agentUpdateData.name = updateDto.name;
        shouldUpdateAgent = true;
      }

      let avatarUploadUrl: string | null = null;
      if (updateDto.avatarMimeType !== undefined && updateDto.avatarMimeType !== null) {
        if (!existingResult.agent.avatar) {
          const avatarKey = generateS3Key({
            baseFolder: updatedBy.toString(),
            categoryFolder: CategoryFolderEnum.AGENT,
          });

          avatarUploadUrl = await this.s3Service.createPresignedWithID(
            avatarKey,
            TimeIntervalEnum.ONE_HOUR,
            ImageType.getType(updateDto.avatarMimeType),
            FileSizeEnum.FIVE_MB,
          );

          agentUpdateData.avatar = avatarKey;
          shouldUpdateAgent = true;
        } else {
          avatarUploadUrl = await this.s3Service.createPresignedWithID(
            existingResult.agent.avatar,
            TimeIntervalEnum.ONE_HOUR,
            ImageType.getType(updateDto.avatarMimeType),
            FileSizeEnum.FIVE_MB,
          );
        }
      }
      if (updateDto.modelConfig !== undefined) {
        // Validate model config if provided
        this.validateModelConfig(updateDto.modelConfig);
        agentUpdateData.modelConfig = updateDto.modelConfig;
        shouldUpdateAgent = true;
      }
      if (updateDto.instruction !== undefined) {
        agentUpdateData.instruction = updateDto.instruction;
        shouldUpdateAgent = true;
      }
      if (updateDto.vectorStoreId !== undefined) {
        // Validate vector store exists nếu không phải null
        if (updateDto.vectorStoreId !== null) {
          await this.validateVectorStore(updateDto.vectorStoreId);
        }
        agentUpdateData.vectorStoreId = updateDto.vectorStoreId;
        shouldUpdateAgent = true;
      }

      if (shouldUpdateAgent) {
        agentUpdateData.updatedAt = Date.now();
        await this.agentRepository.update(id, agentUpdateData);
      }

      // Cập nhật AgentStrategy nếu có thay đổi
      const strategyUpdateData: Partial<AgentStrategy> = {};
      let shouldUpdateStrategy = false;

      if (updateDto.content !== undefined) {
        // Validate content structure if provided
        if (updateDto.content.length > 0) {
          this.validateStrategyContent(updateDto.content);
        }
        strategyUpdateData.content = updateDto.content;
        shouldUpdateStrategy = true;
      }
      if (updateDto.exampleDefault !== undefined) {
        // Validate example structure if provided
        if (updateDto.exampleDefault.length > 0) {
          this.validateStrategyContent(updateDto.exampleDefault);
        }
        strategyUpdateData.exampleDefault = updateDto.exampleDefault;
        shouldUpdateStrategy = true;
      }
      if (updateDto.systemModelId !== undefined) {
        // Validate system model exists nếu không phải null
        if (updateDto.systemModelId !== null) {
          await this.validateSystemModel(updateDto.systemModelId);
        }
        strategyUpdateData.systemModelId = updateDto.systemModelId;
        shouldUpdateStrategy = true;
      }

      if (shouldUpdateStrategy) {
        await this.agentStrategyRepository.updateStrategy(id, strategyUpdateData, updatedBy);
      }

      this.logger.log(`Đã cập nhật chiến lược agent thành công với ID: ${id}`);
      return { id, avatarUploadUrl };
    } catch (error) {
      this.logger.error(`Lỗi khi cập nhật chiến lược agent ${id}: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        AGENT_ERROR_CODES.STRATEGY_UPDATE_FAILED,
        'Không thể cập nhật chiến lược agent'
      );
    }
  }

  /**
   * Xóa mềm agent và chiến lược agent
   * @param id ID của agent và chiến lược
   * @param deletedBy ID nhân viên xóa
   */
  async deleteStrategy(id: string, deletedBy: number): Promise<void> {
    try {
      this.logger.log(`Xóa agent và chiến lược với ID: ${id}`);

      // Kiểm tra sự tồn tại của agent và strategy
      const existingResult = await this.agentStrategyRepository.findById(id);
      if (!existingResult) {
        throw new AppException(
          AGENT_ERROR_CODES.STRATEGY_NOT_FOUND,
          'Không tìm thấy agent và chiến lược để xóa'
        );
      }

      // Kiểm tra xem strategy có đang được sử dụng không
      if (existingResult.strategy.using === true) {
        throw new AppException(
          AGENT_ERROR_CODES.STRATEGY_IN_USE,
          'Không thể xóa chiến lược agent đang được sử dụng'
        );
      }

      // Xóa mềm cả Agent và AgentStrategy
      await Promise.all([
        this.agentRepository.update(id, { deletedAt: Date.now() }),
        this.agentStrategyRepository.softDeleteStrategy(id, deletedBy)
      ]);

      this.logger.log(`Đã xóa agent và chiến lược thành công với ID: ${id}`);
    } catch (error) {
      this.logger.error(`Lỗi khi xóa agent và chiến lược ${id}: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        AGENT_ERROR_CODES.STRATEGY_DELETE_FAILED,
        'Không thể xóa agent và chiến lược'
      );
    }
  }

  /**
   * Khôi phục agent và chiến lược agent đã xóa
   * @param id ID của agent và chiến lược
   */
  async restoreStrategy(id: string): Promise<void> {
    try {
      this.logger.log(`Khôi phục agent và chiến lược với ID: ${id}`);

      // Kiểm tra sự tồn tại của agent và strategy đã xóa
      const deletedResult = await this.agentStrategyRepository.findDeletedById(id);
      if (!deletedResult) {
        throw new AppException(
          AGENT_ERROR_CODES.STRATEGY_NOT_FOUND,
          'Không tìm thấy agent và chiến lược đã xóa để khôi phục'
        );
      }

      // Khôi phục cả Agent và AgentStrategy
      await Promise.all([
        this.agentRepository.update(id, { deletedAt: null }),
        this.agentStrategyRepository.restoreStrategy(id)
      ]);

      this.logger.log(`Đã khôi phục agent và chiến lược thành công với ID: ${id}`);
    } catch (error) {
      this.logger.error(`Lỗi khi khôi phục agent và chiến lược ${id}: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        AGENT_ERROR_CODES.STRATEGY_RESTORE_FAILED,
        'Không thể khôi phục agent và chiến lược'
      );
    }
  }

  /**
   * Validate strategy content structure
   * @param content Strategy content steps
   * @private
   */
  private validateStrategyContent(content: IStrategyContentStep[]): void {
    if (!Array.isArray(content)) {
      throw new AppException(
        AGENT_ERROR_CODES.STRATEGY_CREATION_FAILED,
        'Nội dung chiến lược phải là một mảng'
      );
    }

    for (const step of content) {
      if (!step || typeof step !== 'object') {
        throw new AppException(
          AGENT_ERROR_CODES.STRATEGY_CREATION_FAILED,
          'Mỗi bước trong chiến lược phải là một object'
        );
      }

      if (typeof step.stepOrder !== 'number' || step.stepOrder < 1) {
        throw new AppException(
          AGENT_ERROR_CODES.STRATEGY_CREATION_FAILED,
          'stepOrder phải là số nguyên dương'
        );
      }

      if (!step.content || typeof step.content !== 'string' || !step.content.trim()) {
        throw new AppException(
          AGENT_ERROR_CODES.STRATEGY_CREATION_FAILED,
          'Nội dung của mỗi bước không được để trống'
        );
      }
    }
  }

  /**
   * Validate model config structure
   * @param modelConfig Model configuration
   * @private
   */
  private validateModelConfig(modelConfig: ModelConfig): void {
    if (!modelConfig || typeof modelConfig !== 'object') {
      throw new AppException(
        AGENT_ERROR_CODES.STRATEGY_CREATION_FAILED,
        'Cấu hình model phải là một object'
      );
    }

    // Validate temperature (0-2)
    if (modelConfig.temperature !== undefined) {
      if (typeof modelConfig.temperature !== 'number' || modelConfig.temperature < 0 || modelConfig.temperature > 2) {
        throw new AppException(
          AGENT_ERROR_CODES.STRATEGY_CREATION_FAILED,
          'Temperature phải là số từ 0 đến 2'
        );
      }
    }

    // Validate top_p (0-1)
    if (modelConfig.top_p !== undefined) {
      if (typeof modelConfig.top_p !== 'number' || modelConfig.top_p < 0 || modelConfig.top_p > 1) {
        throw new AppException(
          AGENT_ERROR_CODES.STRATEGY_CREATION_FAILED,
          'Top_p phải là số từ 0 đến 1'
        );
      }
    }

    // Validate top_k (>= 0)
    if (modelConfig.top_k !== undefined) {
      if (typeof modelConfig.top_k !== 'number' || modelConfig.top_k < 0) {
        throw new AppException(
          AGENT_ERROR_CODES.STRATEGY_CREATION_FAILED,
          'Top_k phải là số >= 0'
        );
      }
    }

    // Validate max_tokens (>= 1)
    if (modelConfig.max_tokens !== undefined) {
      if (typeof modelConfig.max_tokens !== 'number' || modelConfig.max_tokens < 1) {
        throw new AppException(
          AGENT_ERROR_CODES.STRATEGY_CREATION_FAILED,
          'max_tokens phải là số nguyên dương'
        );
      }
    }
  }

  /**
   * Validate vector store exists
   * @param vectorStoreId ID của vector store
   * @private
   */
  private async validateVectorStore(vectorStoreId: string): Promise<void> {
    try {
      if (!vectorStoreId || vectorStoreId.trim().length === 0) {
        throw new AppException(
          KNOWLEDGE_FILE_ERROR_CODES.VECTOR_STORE_NOT_FOUND,
          'Vector store ID không hợp lệ'
        );
      }

      // Kiểm tra vector store có tồn tại trong database không
      const vectorStore = await this.vectorStoreRepository
        .createQueryBuilder('vs')
        .select('vs.id')
        .where('vs.id = :id', { id: vectorStoreId })
        .andWhere('vs.deletedAt IS NULL')
        .getOne();

      if (!vectorStore) {
        throw new AppException(
          KNOWLEDGE_FILE_ERROR_CODES.VECTOR_STORE_NOT_FOUND,
          `Vector store với ID ${vectorStoreId} không tồn tại hoặc đã bị xóa`
        );
      }

      this.logger.log(`Vector store validation passed for ID: ${vectorStoreId}`);
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Lỗi khi validate vector store ${vectorStoreId}: ${error.message}`, error.stack);
      throw new AppException(
        KNOWLEDGE_FILE_ERROR_CODES.VECTOR_STORE_NOT_FOUND,
        'Không thể xác thực vector store'
      );
    }
  }

  /**
   * Validate system model exists
   * @param systemModelId ID của system model
   * @private
   */
  private async validateSystemModel(systemModelId: string): Promise<void> {
    try {
      if (!systemModelId || systemModelId.trim().length === 0) {
        throw new AppException(
          MODELS_ERROR_CODES.MODEL_NOT_FOUND,
          'System model ID không hợp lệ'
        );
      }

      // Kiểm tra system model có tồn tại trong database không
      const exists = await this.systemModelsRepository.isExists(systemModelId);

      if (!exists) {
        throw new AppException(
          MODELS_ERROR_CODES.MODEL_NOT_FOUND,
          `System model với ID ${systemModelId} không tồn tại`
        );
      }

      this.logger.log(`System model validation passed for ID: ${systemModelId}`);
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Lỗi khi validate system model ${systemModelId}: ${error.message}`, error.stack);
      throw new AppException(
        MODELS_ERROR_CODES.MODEL_NOT_FOUND,
        'Không thể xác thực system model'
      );
    }
  }
}






