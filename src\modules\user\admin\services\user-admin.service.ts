import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Like, FindOptionsWhere } from 'typeorm';
import { User, UserStatusLog } from '@modules/user/entities';
import { BlockUserDto, UnblockUserDto, UserDetailDto, UserListItemDto, UserQueryDto } from '../dto';
import { PaginatedResult } from '@/common/response';
import { AppException, ErrorCode } from '@common/exceptions';
import { CdnService } from '@shared/services/cdn.service';
import { TimeIntervalEnum } from '@shared/utils';
import { UserStatusEventEnum } from '@modules/user/enums/user-status-event.enum';
import { UserStatusLogRepository } from '@modules/user/repositories/user-status-log.repository';
import { EmailPlaceholderService } from '@modules/email/services/email-placeholder.service';

@Injectable()
export class UserAdminService {
  private readonly logger = new Logger(UserAdminService.name);

  constructor(
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    private readonly cdnService: CdnService,
    private readonly userStatusLogRepository: UserStatusLogRepository,
    private readonly emailPlaceholderService: EmailPlaceholderService
  ) {}

  /**
   * Lấy danh sách người dùng với phân trang và lọc
   * @param queryDto Tham số truy vấn
   * @returns Danh sách người dùng đã phân trang
   */
  async findAll(queryDto: UserQueryDto): Promise<PaginatedResult<UserListItemDto>> {
    try {
      const {
        page = 1,
        limit = 10,
        search,
        sortBy = 'createdAt',
        sortDirection = 'DESC',
        isActive,
        type,
        email,
        phoneNumber
      } = queryDto;

      // Xây dựng điều kiện tìm kiếm
      const where: FindOptionsWhere<User> = {};

      // Thêm điều kiện tìm kiếm theo từ khóa
      if (search) {
        where.fullName = Like(`%${search}%`);
      }

      // Thêm điều kiện lọc theo trạng thái
      if (isActive !== undefined) {
        where.isActive = isActive;
      }

      // Thêm điều kiện lọc theo loại tài khoản
      if (type) {
        where.type = type;
      }

      // Thêm điều kiện lọc theo email
      if (email) {
        where.email = Like(`%${email}%`);
      }

      // Thêm điều kiện lọc theo số điện thoại
      if (phoneNumber) {
        where.phoneNumber = Like(`%${phoneNumber}%`);
      }

      // Tính toán skip cho phân trang
      const skip = (page - 1) * limit;

      // Thực hiện truy vấn
      const [users, totalItems] = await this.userRepository.findAndCount({
        where,
        order: { [sortBy]: sortDirection },
        skip,
        take: limit
      });

      // Chuyển đổi kết quả sang DTO
      const items = users.map(user => this.mapToUserListItemDto(user));

      // Tính toán metadata phân trang
      const totalPages = Math.ceil(totalItems / limit);

      return {
        items,
        meta: {
          totalItems,
          itemCount: items.length,
          itemsPerPage: limit,
          totalPages,
          currentPage: page
        }
      };
    } catch (error) {
      this.logger.error(`Error finding users: ${error.message}`, error.stack);
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        'Lỗi khi lấy danh sách người dùng'
      );
    }
  }

  /**
   * Lấy thông tin chi tiết người dùng
   * @param id ID của người dùng
   * @returns Thông tin chi tiết người dùng
   */
  async findOne(id: number): Promise<UserDetailDto> {
    try {
      // Tìm người dùng theo ID
      const user = await this.userRepository.findOne({ where: { id } });

      // Kiểm tra người dùng tồn tại
      if (!user) {
        throw new AppException(
          ErrorCode.USER_NOT_FOUND,
          `Không tìm thấy người dùng với ID ${id}`
        );
      }

      // Chuyển đổi kết quả sang DTO
      return this.mapToUserDetailDto(user);
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }

      this.logger.error(`Error finding user with ID ${id}: ${error.message}`, error.stack);
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        'Lỗi khi lấy thông tin người dùng'
      );
    }
  }

  /**
   * Chuyển đổi entity User sang UserListItemDto
   * @param user Entity User
   * @returns UserListItemDto
   */
  private mapToUserListItemDto(user: User): UserListItemDto {
    // Xử lý URL ảnh đại diện
    let avatarUrl: string | null = null;
    if (user.avatar) {
      avatarUrl = this.cdnService.generateUrlView(
        user.avatar,
        TimeIntervalEnum.ONE_HOUR
      );
    }

    return {
      id: user.id,
      fullName: user.fullName,
      email: user.email,
      phoneNumber: user.phoneNumber,
      isActive: user.isActive,
      isVerifyEmail: user.isVerifyEmail,
      createdAt: user.createdAt,
      pointsBalance: user.pointsBalance,
      type: user.type,
      avatarUrl
    };
  }

  /**
   * Chuyển đổi entity User sang UserDetailDto
   * @param user Entity User
   * @returns UserDetailDto
   */
  private mapToUserDetailDto(user: User): UserDetailDto {
    // Xử lý URL ảnh đại diện
    let avatarUrl: string | null = null;
    if (user.avatar) {
      avatarUrl = this.cdnService.generateUrlView(
        user.avatar,
        TimeIntervalEnum.ONE_HOUR
      );
    }

    return {
      id: user.id,
      fullName: user.fullName,
      email: user.email,
      phoneNumber: user.phoneNumber,
      isActive: user.isActive,
      isVerifyEmail: user.isVerifyEmail,
      isVerifyPhone: user.isVerifyPhone,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt,
      address: user.address,
      taxCode: user.taxCode,
      pointsBalance: user.pointsBalance,
      type: user.type,
      platform: user.platform,
      citizenId: user.citizenId,
      citizenIssuePlace: user.citizenIssuePlace,
      citizenIssueDate: user.citizenIssueDate,
      avatarUrl,
      dateOfBirth: user.dateOfBirth,
      gender: user.gender,
      bankCode: user.bankCode,
      accountNumber: user.accountNumber,
      accountHolder: user.accountHolder,
      bankBranch: user.bankBranch,
      affiliateAccountId: user.affiliateAccountId
    };
  }

  /**
   * Khóa tài khoản người dùng
   * @param id ID của người dùng
   * @param blockUserDto Thông tin khóa tài khoản
   * @param employeeId ID của admin thực hiện hành động
   * @returns Thông tin người dùng đã khóa
   */
  async blockUser(id: number, blockUserDto: BlockUserDto, employeeId: number): Promise<UserDetailDto> {
    try {
      // Tìm người dùng theo ID
      const user = await this.userRepository.findOne({ where: { id } });

      // Kiểm tra người dùng tồn tại
      if (!user) {
        throw new AppException(
          ErrorCode.USER_NOT_FOUND,
          `Không tìm thấy người dùng với ID ${id}`
        );
      }

      // Kiểm tra người dùng đã bị khóa chưa
      if (!user.isActive) {
        throw new AppException(
          ErrorCode.VALIDATION_ERROR,
          `Người dùng với ID ${id} đã bị khóa trước đó`
        );
      }

      // Cập nhật trạng thái người dùng
      user.isActive = false;
      user.updatedAt = Date.now();

      // Lưu thay đổi vào cơ sở dữ liệu
      await this.userRepository.save(user);

      // Ghi log
      await this.userStatusLogRepository.create(
        id,
        UserStatusEventEnum.USER_BLOCK,
        blockUserDto.reason,
        employeeId,
        blockUserDto.info
      );

      // Gửi email thông báo khóa tài khoản
      if (user.email) {
        try {
          await this.emailPlaceholderService.sendAccountSuspension({
            EMAIL: user.email,
            USER_ID: user.id.toString(),
            NAME: user.fullName || 'Người dùng',
            DATE_NOW: new Date().toLocaleDateString('vi-VN'),
            USER_REASON_DISABLE: blockUserDto.reason
          });
          this.logger.log(`Đã gửi email thông báo khóa tài khoản đến: ${user.email}`);
        } catch (emailError) {
          // Log lỗi email nhưng không throw để không ảnh hưởng đến việc khóa tài khoản
          this.logger.error(`Lỗi khi gửi email thông báo khóa tài khoản: ${emailError.message}`, emailError.stack);
        }
      }

      // Trả về thông tin người dùng đã cập nhật
      return this.mapToUserDetailDto(user);
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }

      this.logger.error(`Error blocking user with ID ${id}: ${error.message}`, error.stack);
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        'Lỗi khi khóa tài khoản người dùng'
      );
    }
  }

  /**
   * Mở khóa tài khoản người dùng
   * @param id ID của người dùng
   * @param unblockUserDto Thông tin mở khóa tài khoản
   * @param employeeId ID của admin thực hiện hành động
   * @returns Thông tin người dùng đã mở khóa
   */
  async unblockUser(id: number, unblockUserDto: UnblockUserDto, employeeId: number): Promise<UserDetailDto> {
    try {
      // Tìm người dùng theo ID
      const user = await this.userRepository.findOne({ where: { id } });

      // Kiểm tra người dùng tồn tại
      if (!user) {
        throw new AppException(
          ErrorCode.USER_NOT_FOUND,
          `Không tìm thấy người dùng với ID ${id}`
        );
      }

      // Kiểm tra người dùng đã bị khóa chưa
      if (user.isActive) {
        throw new AppException(
          ErrorCode.VALIDATION_ERROR,
          `Người dùng với ID ${id} đã được kích hoạt trước đó`
        );
      }

      // Cập nhật trạng thái người dùng
      user.isActive = true;
      user.updatedAt = Date.now();

      // Lưu thay đổi vào cơ sở dữ liệu
      await this.userRepository.save(user);

      // Ghi log
      await this.userStatusLogRepository.create(
        id,
        UserStatusEventEnum.USER_UNBLOCK,
        unblockUserDto.reason,
        employeeId,
        unblockUserDto.info
      );

      // Trả về thông tin người dùng đã cập nhật
      return this.mapToUserDetailDto(user);
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }

      this.logger.error(`Error unblocking user with ID ${id}: ${error.message}`, error.stack);
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        'Lỗi khi mở khóa tài khoản người dùng'
      );
    }
  }

  /**
   * Lấy lịch sử thay đổi trạng thái của người dùng
   * @param userId ID của người dùng
   * @returns Danh sách log thay đổi trạng thái
   */
  async getUserStatusLogs(userId: number): Promise<UserStatusLog[]> {
    try {
      // Kiểm tra người dùng tồn tại
      const user = await this.userRepository.findOne({ where: { id: userId } });
      if (!user) {
        throw new AppException(
          ErrorCode.USER_NOT_FOUND,
          `Không tìm thấy người dùng với ID ${userId}`
        );
      }

      // Lấy danh sách log
      return this.userStatusLogRepository.findByUserId(userId);
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }

      this.logger.error(`Error getting status logs for user with ID ${userId}: ${error.message}`, error.stack);
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        'Lỗi khi lấy lịch sử thay đổi trạng thái người dùng'
      );
    }
  }
}
