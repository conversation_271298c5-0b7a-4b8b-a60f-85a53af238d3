import { validate } from 'class-validator';
import { IsInternationalPhone, IsInternationalPhoneWithCountry, formatInternationalPhone, getPhoneInfo } from './international-phone.validator';

class TestPhoneDto {
  @IsInternationalPhone()
  phone: string;
}

class TestPhoneWithCountryDto {
  countryCode: string;
  
  @IsInternationalPhoneWithCountry()
  phone: string;
}

describe('InternationalPhoneValidator', () => {
  describe('@IsInternationalPhone', () => {
    it('should validate valid international phone numbers', async () => {
      const dto = new TestPhoneDto();
      
      // Test valid phone numbers
      const validPhones = [
        '+84912345678',
        '+1234567890',
        '+447911123456',
        '+8613800138000',
        '+819012345678',
        '+821012345678'
      ];

      for (const phone of validPhones) {
        dto.phone = phone;
        const errors = await validate(dto);
        expect(errors).toHaveLength(0);
      }
    });

    it('should reject invalid phone numbers', async () => {
      const dto = new TestPhoneDto();
      
      // Test invalid phone numbers
      const invalidPhones = [
        '84912345678', // Missing +
        '+84', // Too short
        '+8491234567890123456789', // Too long
        'invalid', // Not a number
        '+84abc123456', // Contains letters
        '', // Empty
      ];

      for (const phone of invalidPhones) {
        dto.phone = phone;
        const errors = await validate(dto);
        expect(errors.length).toBeGreaterThan(0);
      }
    });

    it('should allow empty phone when optional', async () => {
      const dto = new TestPhoneDto();
      dto.phone = '';
      
      const errors = await validate(dto);
      // Should pass validation as empty values are handled by @IsOptional
      expect(errors).toHaveLength(0);
    });
  });

  describe('@IsInternationalPhoneWithCountry', () => {
    it('should validate phone with country code', async () => {
      const dto = new TestPhoneWithCountryDto();
      dto.countryCode = '+84';
      dto.phone = '912345678';
      
      const errors = await validate(dto);
      expect(errors).toHaveLength(0);
    });

    it('should reject invalid phone with country code', async () => {
      const dto = new TestPhoneWithCountryDto();
      dto.countryCode = '+84';
      dto.phone = '123'; // Too short
      
      const errors = await validate(dto);
      expect(errors.length).toBeGreaterThan(0);
    });

    it('should validate phone without country code as international', async () => {
      const dto = new TestPhoneWithCountryDto();
      dto.countryCode = '';
      dto.phone = '+84912345678';
      
      const errors = await validate(dto);
      expect(errors).toHaveLength(0);
    });
  });

  describe('formatInternationalPhone', () => {
    it('should format valid phone numbers', () => {
      expect(formatInternationalPhone('+84912345678')).toBe('+84 91 234 5678');
      expect(formatInternationalPhone('+1234567890')).toBe('****** 567 890');
    });

    it('should return null for invalid phone numbers', () => {
      expect(formatInternationalPhone('invalid')).toBeNull();
      expect(formatInternationalPhone('')).toBeNull();
      expect(formatInternationalPhone('123')).toBeNull();
    });
  });

  describe('getPhoneInfo', () => {
    it('should return detailed info for valid phone numbers', () => {
      const info = getPhoneInfo('+84912345678');
      
      expect(info).toEqual({
        isValid: true,
        international: '+84 91 234 5678',
        national: '************',
        e164: '+84912345678',
        country: 'VN',
        countryCallingCode: '84',
        type: 'MOBILE',
      });
    });

    it('should return invalid info for invalid phone numbers', () => {
      const info = getPhoneInfo('invalid');
      
      expect(info.isValid).toBe(false);
      expect(info.international).toBeNull();
      expect(info.national).toBeNull();
      expect(info.e164).toBeNull();
      expect(info.country).toBeNull();
      expect(info.countryCallingCode).toBeNull();
      expect(info.type).toBeNull();
    });

    it('should return null for empty phone', () => {
      const info = getPhoneInfo('');
      expect(info).toBeNull();
    });
  });
});
