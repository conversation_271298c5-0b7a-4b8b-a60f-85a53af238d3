import { ApiProperty } from '@nestjs/swagger';
import { IsNumber, IsString } from 'class-validator';
import { IStrategyContentStep } from '@modules/agent/interfaces/strategy-content-step.interface';

/**
 * DTO cho một bước trong nội dung chiến lược
 */
export class StrategyContentStepDto implements IStrategyContentStep {
  /**
   * Thứ tự của bước
   */
  @ApiProperty({
    description: 'Thứ tự của bước',
    example: 1,
  })
  @IsNumber()
  stepOrder: number;

  /**
   * Nội dung của bước
   */
  @ApiProperty({
    description: 'Nội dung của bước',
    example: 'Bước đầu tiên: Phân tích yêu cầu',
  })
  @IsString()
  content: string;
}
