import { HttpStatus } from '@nestjs/common';
import { ErrorCode } from '@/common/exceptions/app.exception';

/**
 * Mã lỗi cho module rule-contract
 * Phạm vi mã lỗi: 15000-15099
 */
export const RULE_CONTRACT_ERROR_CODES = {
  CONTRACT_NOT_FOUND: new ErrorCode(
    15000,
    'Không tìm thấy hợp đồng nguyên tắc',
    HttpStatus.NOT_FOUND,
  ),
  CONTRACT_RETRIEVAL_FAILED: new ErrorCode(
    15001,
    'Lỗi khi lấy thông tin hợp đồng nguyên tắc',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
  UNAUTHORIZED_ACCESS: new ErrorCode(
    15002,
    'Không có quyền truy cập hợp đồng này',
    HttpStatus.FORBIDDEN,
  ),
  PERMISSION_DENIED: new ErrorCode(
    15003,
    '<PERSON><PERSON><PERSON><PERSON> có quyền thực hiện hành động này',
    HttpStatus.FORBIDDEN,
  ),
  INVALID_OPERATION: new ErrorCode(
    15004,
    'Thao tác không hợp lệ',
    HttpStatus.BAD_REQUEST,
  ),
  INVALID_SIGNATURE: new ErrorCode(
    15005,
    'Chữ ký không hợp lệ',
    HttpStatus.BAD_REQUEST,
  ),
  INVALID_CONTRACT_STATE: new ErrorCode(
    15006,
    'Trạng thái hợp đồng không hợp lệ',
    HttpStatus.BAD_REQUEST,
  ),
  CONTRACT_CREATION_FAILED: new ErrorCode(
    15007,
    'Lỗi khi tạo hợp đồng',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
  CONTRACT_UPDATE_FAILED: new ErrorCode(
    15008,
    'Lỗi khi cập nhật hợp đồng',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
  CONTRACT_SIGNING_FAILED: new ErrorCode(
    15009,
    'Lỗi khi ký hợp đồng',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
  CONTRACT_APPROVAL_FAILED: new ErrorCode(
    15010,
    'Lỗi khi phê duyệt hợp đồng',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
  CONTRACT_REJECTION_FAILED: new ErrorCode(
    15011,
    'Lỗi khi từ chối hợp đồng',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
  INVALID_DATA: new ErrorCode(
    15012,
    'Dữ liệu không hợp lệ',
    HttpStatus.BAD_REQUEST,
  ),
  EMAIL_SENDING_FAILED: new ErrorCode(
    15013,
    'Lỗi khi gửi email',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
  OTP_GENERATION_FAILED: new ErrorCode(
    15014,
    'Lỗi khi tạo OTP',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
  OTP_EXPIRED: new ErrorCode(
    15015,
    'OTP đã hết hạn',
    HttpStatus.BAD_REQUEST,
  ),
  INVALID_OTP: new ErrorCode(
    15016,
    'OTP không chính xác',
    HttpStatus.BAD_REQUEST,
  ),
  OTP_VERIFICATION_FAILED: new ErrorCode(
    15017,
    'Lỗi khi xác thực OTP',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
  SIGNATURE_UPLOAD_FAILED: new ErrorCode(
    15018,
    'Lỗi khi tạo URL upload chữ ký',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
  SIGNATURE_NOT_FOUND: new ErrorCode(
    15019,
    'Không tìm thấy file chữ ký',
    HttpStatus.NOT_FOUND,
  ),
  SIGNATURE_VERIFICATION_FAILED: new ErrorCode(
    15020,
    'Lỗi khi xác thực chữ ký',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
  SIGNATURE_URL_GENERATION_FAILED: new ErrorCode(
    15021,
    'Lỗi khi tạo URL xem chữ ký',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
};
