import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsOptional, IsString, IsNumber, IsDateString } from 'class-validator';
import { Transform } from 'class-transformer';
import { AuthMethodEnum } from '../enums/auth-method.enum';
import { AuthStatusEnum } from '../enums/auth-status.enum';

/**
 * DTO cho query danh sách log xác thực
 */
export class AuthVerificationLogQueryDto {
  @ApiProperty({
    description: 'Phương thức xác thực',
    enum: AuthMethodEnum,
    required: false,
  })
  @IsOptional()
  @IsEnum(AuthMethodEnum)
  authMethod?: AuthMethodEnum;

  @ApiProperty({
    description: 'Trạng thái xác thực',
    enum: AuthStatusEnum,
    required: false,
  })
  @IsOptional()
  @IsEnum(AuthStatusEnum)
  status?: AuthStatusEnum;

  @ApiProperty({
    description: 'Địa chỉ IP',
    required: false,
  })
  @IsOptional()
  @IsString()
  ipAddress?: string;

  @ApiProperty({
    description: 'Từ ngày (timestamp)',
    required: false,
    example: 1640995200000,
  })
  @IsOptional()
  @Transform(({ value }) => parseInt(value))
  @IsNumber()
  fromDate?: number;

  @ApiProperty({
    description: 'Đến ngày (timestamp)',
    required: false,
    example: 1640995200000,
  })
  @IsOptional()
  @Transform(({ value }) => parseInt(value))
  @IsNumber()
  toDate?: number;

  @ApiProperty({
    description: 'Số trang',
    required: false,
    default: 1,
  })
  @IsOptional()
  @Transform(({ value }) => parseInt(value))
  @IsNumber()
  page?: number = 1;

  @ApiProperty({
    description: 'Số bản ghi trên mỗi trang',
    required: false,
    default: 10,
  })
  @IsOptional()
  @Transform(({ value }) => parseInt(value))
  @IsNumber()
  limit?: number = 10;
}

/**
 * DTO cho response log xác thực
 */
export class AuthVerificationLogResponseDto {
  @ApiProperty({
    description: 'ID của log',
    example: 1,
  })
  id: number;

  @ApiProperty({
    description: 'ID của người dùng',
    example: 1,
  })
  userId: number;

  @ApiProperty({
    description: 'Phương thức xác thực',
    enum: AuthMethodEnum,
    example: AuthMethodEnum.EMAIL,
  })
  authMethod: AuthMethodEnum;

  @ApiProperty({
    description: 'Trạng thái xác thực',
    enum: AuthStatusEnum,
    example: AuthStatusEnum.SUCCESS,
  })
  status: AuthStatusEnum;

  @ApiProperty({
    description: 'Địa chỉ IP',
    example: '***********',
  })
  ipAddress: string;

  @ApiProperty({
    description: 'User agent',
    example: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
  })
  userAgent: string;

  @ApiProperty({
    description: 'Thời gian gửi mã (timestamp)',
    example: 1640995200000,
  })
  codeSentAt: number;

  @ApiProperty({
    description: 'Thời gian xác thực (timestamp)',
    example: 1640995200000,
  })
  verifiedAt: number;

  @ApiProperty({
    description: 'Số lần thử',
    example: 1,
  })
  attemptCount: number;

  @ApiProperty({
    description: 'Thời gian tạo (timestamp)',
    example: 1640995200000,
  })
  createdAt: number;
}
