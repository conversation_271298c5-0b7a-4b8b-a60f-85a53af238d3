import { Column, Entity, PrimaryColumn } from 'typeorm';
import { AgentTemplateStatus } from '@modules/agent/constants';
import { ConversionConfig } from '@modules/agent/interfaces/convert-config.interface';
import { ProfileAgent } from '@modules/agent/interfaces/profile-agent.interface';

/**
 * Entity đại diện cho bảng agents_template trong cơ sở dữ liệu
 * Bảng lưu mẫu agent để tạo agent mới, dùng bởi admin
 */
@Entity('agents_template')
export class AgentTemplate {
  /**
   * UUID tham chiếu từ agents.id
   */
  @PrimaryColumn('uuid')
  id: string;

  /**
   * ID nhân viên tạo
   */
  @Column({ name: 'created_by', type: 'integer', nullable: true })
  createdBy: number | null;

  /**
   * ID nhân viên cập nhật
   */
  @Column({ name: 'updated_by', type: 'integer', nullable: true })
  updatedBy: number | null;

  /**
   * ID nhân viên xóa
   */
  @Column({ name: 'deleted_by', type: 'integer', nullable: true })
  deletedBy: number | null;

  /**
   * ID loại chức năng agent, tham chiếu type_agents
   */
  @Column({ name: 'type_id' })
  typeId: number;

  /**
   * Thông tin hồ sơ mẫu dạng JSONB
   */
  @Column({ type: 'jsonb', default: '{}' })
  profile: ProfileAgent;

  /**
   * Cấu hình chuyển đổi mẫu dạng JSONB
   */
  @Column({ name: 'convert_config', type: 'jsonb', default: '[]' })
  convertConfig: ConversionConfig[];

  /**
   * Trạng thái có thể bán
   */
  @Column({ name: 'is_for_sale', type: 'boolean', default: false })
  isForSale: boolean;

  /**
   * UUID tham chiếu đến bảng system_models
   * ID của model system (thay thế modelBaseId)
   */
  @Column({ name: 'model_system_id', type: 'uuid', nullable: true })
  modelSystemId: string | null;

  /**
   * UUID tham chiếu đến bảng agents_strategy
   * ID của strategy
   */
  @Column({ name: 'strategy_id', type: 'uuid', nullable: true })
  strategyId: string | null;
}
