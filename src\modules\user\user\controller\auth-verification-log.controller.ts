import {
  Controller,
  Get,
  Query,
  Param,
  ParseIntPipe,
  UseGuards,
  NotFoundException,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
  ApiExtraModels,
  getSchemaPath,
} from '@nestjs/swagger';
import { AuthVerificationLogService } from '../service/auth-verification-log.service';
import { AuthVerificationLogQueryDto, AuthVerificationLogResponseDto } from '../../dto/auth-verification-log.dto';
import { ApiResponseDto, PaginatedResult } from '@common/response';
import { JwtUserGuard } from '@modules/auth/guards';
import { CurrentUser } from '@/modules/auth/decorators/current-user.decorator';
import { JWTPayload } from '@/modules/auth/interfaces/jwt-payload.interface';
import { SWAGGER_API_TAGS } from '@/common/swagger';

@ApiTags(SWAGGER_API_TAGS.USER_AUTH_VERIFICATION_LOG)
@ApiBearerAuth('JWT-auth')
@ApiExtraModels(
  ApiResponseDto,
  PaginatedResult,
  AuthVerificationLogResponseDto,
)
@UseGuards(JwtUserGuard)
@Controller('user/auth-verification-logs')
export class AuthVerificationLogController {
  constructor(
    private readonly authVerificationLogService: AuthVerificationLogService,
  ) {}

  @Get()
  @ApiOperation({ 
    summary: 'Lấy danh sách log xác thực của người dùng hiện tại',
    description: 'API này cho phép người dùng xem lịch sử xác thực của mình với các bộ lọc và phân trang'
  })
  @ApiResponse({
    status: 200,
    description: 'Lấy danh sách log xác thực thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            code: { type: 'number', example: 200 },
            message: { type: 'string', example: 'Lấy danh sách log xác thực thành công' },
            result: {
              allOf: [
                { $ref: getSchemaPath(PaginatedResult) },
                {
                  properties: {
                    items: {
                      type: 'array',
                      items: { $ref: getSchemaPath(AuthVerificationLogResponseDto) }
                    }
                  }
                }
              ]
            }
          }
        }
      ]
    }
  })
  @ApiResponse({ status: 401, description: 'Không có quyền truy cập' })
  async getAuthVerificationLogs(
    @CurrentUser() user: JWTPayload,
    @Query() queryDto: AuthVerificationLogQueryDto,
  ): Promise<ApiResponseDto<PaginatedResult<AuthVerificationLogResponseDto>>> {
    const result = await this.authVerificationLogService.getAuthVerificationLogs(
      user.id,
      queryDto,
    );

    return ApiResponseDto.success(
      result,
      'Lấy danh sách log xác thực thành công',
    );
  }

  @Get('stats')
  @ApiOperation({ 
    summary: 'Lấy thống kê log xác thực của người dùng hiện tại',
    description: 'API này cung cấp thống kê tổng quan về các lần xác thực của người dùng'
  })
  @ApiResponse({
    status: 200,
    description: 'Lấy thống kê log xác thực thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            code: { type: 'number', example: 200 },
            message: { type: 'string', example: 'Lấy thống kê log xác thực thành công' },
            result: {
              type: 'object',
              properties: {
                totalAttempts: { type: 'number', example: 100, description: 'Tổng số lần thử xác thực' },
                successfulAttempts: { type: 'number', example: 95, description: 'Số lần xác thực thành công' },
                failedAttempts: { type: 'number', example: 5, description: 'Số lần xác thực thất bại' },
                successRate: { type: 'number', example: 95.0, description: 'Tỷ lệ thành công (%)' },
                methodStats: {
                  type: 'array',
                  items: {
                    type: 'object',
                    properties: {
                      method: { type: 'string', example: 'EMAIL', description: 'Phương thức xác thực' },
                      count: { type: 'number', example: 50, description: 'Số lần sử dụng' },
                      successCount: { type: 'number', example: 48, description: 'Số lần thành công' }
                    }
                  },
                  description: 'Thống kê theo phương thức xác thực'
                }
              }
            }
          }
        }
      ]
    }
  })
  @ApiResponse({ status: 401, description: 'Không có quyền truy cập' })
  async getAuthVerificationStats(
    @CurrentUser() user: JWTPayload,
    @Query('fromDate') fromDate?: number,
    @Query('toDate') toDate?: number,
  ): Promise<ApiResponseDto<any>> {
    const result = await this.authVerificationLogService.getAuthVerificationStats(
      user.id,
      fromDate,
      toDate,
    );

    return ApiResponseDto.success(
      result,
      'Lấy thống kê log xác thực thành công',
    );
  }

  @Get(':id')
  @ApiOperation({ 
    summary: 'Lấy chi tiết log xác thực theo ID',
    description: 'API này cho phép người dùng xem chi tiết một log xác thực cụ thể'
  })
  @ApiParam({
    name: 'id',
    description: 'ID của log xác thực',
    type: 'number',
    example: 1,
  })
  @ApiResponse({
    status: 200,
    description: 'Lấy chi tiết log xác thực thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            code: { type: 'number', example: 200 },
            message: { type: 'string', example: 'Lấy chi tiết log xác thực thành công' },
            result: { $ref: getSchemaPath(AuthVerificationLogResponseDto) }
          }
        }
      ]
    }
  })
  @ApiResponse({ status: 401, description: 'Không có quyền truy cập' })
  @ApiResponse({ status: 404, description: 'Không tìm thấy log xác thực' })
  async getAuthVerificationLogById(
    @CurrentUser() user: JWTPayload,
    @Param('id', ParseIntPipe) logId: number,
  ): Promise<ApiResponseDto<AuthVerificationLogResponseDto>> {
    const result = await this.authVerificationLogService.getAuthVerificationLogById(
      user.id,
      logId,
    );

    if (!result) {
      throw new NotFoundException('Không tìm thấy log xác thực');
    }

    return ApiResponseDto.success(
      result,
      'Lấy chi tiết log xác thực thành công',
    );
  }
}
