import { PdfPosition } from '@shared/interface/pdf-edit.interface';
import { ContractTypeEnum } from '@/shared/enums/contract-type.enum';
import { UserTypeEnum } from '@modules/user/enums';

/**
 * Utility class để tạo các vị trí chèn chữ ký và text vào PDF
 */
export class PdfPositionUtils {
  /**
   * Lấy vị trí chữ ký của admin trên hợp đồng
   * @param contract Loại hợp đồng
   * @param type Loại người dùng
   * @param signatureBase64 Chuỗi Base64 của chữ ký
   * @returns Danh sách vị trí chữ ký
   */
  static getPositionSignatureAdmin(
    contract: ContractTypeEnum,
    type: UserTypeEnum,
    signatureBase64: string,
  ): PdfPosition[] {
    const positions: PdfPosition[] = [];

    switch (contract) {
      case ContractTypeEnum.RULE_CONTRACT:
        if (type === UserTypeEnum.BUSINESS) {
          const position: PdfPosition = {
            signatureBase64,
            signatureWidthMm: 30,
            signatureHeightMm: 20,
            pageIndex: 13,
            xMm: 120,
            yMm: 75,
          };
          positions.push(position);
        } else if (type === UserTypeEnum.INDIVIDUAL) {
          const position: PdfPosition = {
            signatureBase64,
            signatureWidthMm: 30,
            signatureHeightMm: 20,
            pageIndex: 13,
            xMm: 120,
            yMm: 115,
          };
          positions.push(position);
        }
        break;

      case ContractTypeEnum.AFFILIATE_CONTRACT:
        if (type === UserTypeEnum.BUSINESS) {
          const position: PdfPosition = {
            signatureBase64,
            signatureWidthMm: 30,
            signatureHeightMm: 30,
            pageIndex: 14,
            xMm: 50,
            yMm: 220,
          };
          positions.push(position);
        } else if (type === UserTypeEnum.INDIVIDUAL) {
          const position: PdfPosition = {
            signatureBase64,
            signatureWidthMm: 30,
            signatureHeightMm: 30,
            pageIndex: 15,
            xMm: 50,
            yMm: 50,
          };
          positions.push(position);
        }
        break;
    }

    return positions;
  }

  /**
   * Lấy vị trí chữ ký của khách hàng trên hợp đồng
   * @param contract Loại hợp đồng
   * @param type Loại người dùng
   * @param signatureBase64 Chuỗi Base64 của chữ ký
   * @returns Danh sách vị trí chữ ký
   */
  static getPositionSignatureCustomer(
    contract: ContractTypeEnum,
    type: UserTypeEnum,
    signatureBase64: string,
  ): PdfPosition[] {
    const positions: PdfPosition[] = [];

    switch (contract) {
      case ContractTypeEnum.RULE_CONTRACT:
        if (type === UserTypeEnum.BUSINESS) {
          const position: PdfPosition = {
            signatureBase64,
            signatureWidthMm: 30,
            signatureHeightMm: 30,
            pageIndex: 13,
            xMm: 48,
            yMm: 67,
          };
          positions.push(position);
        } else if (type === UserTypeEnum.INDIVIDUAL) {
          const position: PdfPosition = {
            signatureBase64,
            signatureWidthMm: 30,
            signatureHeightMm: 30,
            pageIndex: 13,
            xMm: 48,
            yMm: 110,
          };
          positions.push(position);
        }
        break;

      case ContractTypeEnum.AFFILIATE_CONTRACT:
        if (type === UserTypeEnum.BUSINESS) {
          const position: PdfPosition = {
            signatureBase64,
            signatureWidthMm: 30,
            signatureHeightMm: 30,
            pageIndex: 14,
            xMm: 125,
            yMm: 220,
          };
          positions.push(position);
        } else if (type === UserTypeEnum.INDIVIDUAL) {
          const position: PdfPosition = {
            signatureBase64,
            signatureWidthMm: 30,
            signatureHeightMm: 30,
            pageIndex: 15,
            xMm: 125,
            yMm: 50,
          };
          positions.push(position);
        }
        break;
    }

    return positions;
  }

  /**
   * Tạo vị trí text cho hợp đồng affiliate của khách hàng cá nhân
   * @param userId ID của người dùng
   * @param time Thời gian tạo hợp đồng (timestamp)
   * @param name Tên người dùng
   * @param dateOfBirth Ngày sinh
   * @param cccd Số CCCD
   * @param issueDate Ngày cấp CCCD
   * @param issuePlace Nơi cấp CCCD
   * @param phoneNumber Số điện thoại
   * @param address Địa chỉ
   * @param taxCode Mã số thuế
   * @returns Danh sách vị trí text
   */
  static affiliateContractCustomer(
    userId: number,
    time: number,
    name: string,
    dateOfBirth: Date,
    cccd: string,
    issueDate: Date,
    issuePlace: string,
    phoneNumber: string,
    address: string,
    taxCode?: string,
  ): PdfPosition[] {
    const pdfPositions: PdfPosition[] = [];

    // Định dạng ngày/tháng/năm
    const formatter = (date: Date): string => {
      const day = date.getDate().toString().padStart(2, '0');
      const month = (date.getMonth() + 1).toString().padStart(2, '0');
      const year = date.getFullYear();
      return `${day}/${month}/${year}`;
    };

    // Chuyển đổi timestamp thành ngày
    const formattedTime = formatter(new Date(time));

    pdfPositions.push({ pageIndex: 1, text: `${userId}CN`, size: 12, xMm: 119, yMm: 87 });
    pdfPositions.push({ pageIndex: 1, text: formattedTime, size: 10, xMm: 49, yMm: 163 });
    pdfPositions.push({ pageIndex: 2, text: name, size: 12, xMm: 77, yMm: 25.5 });
    pdfPositions.push({ pageIndex: 2, text: formatter(dateOfBirth), size: 10, xMm: 57.5, yMm: 32.5 });
    pdfPositions.push({ pageIndex: 2, text: cccd, size: 10, xMm: 57.5, yMm: 39.4 });
    pdfPositions.push({ pageIndex: 2, text: formatter(issueDate), size: 10, xMm: 57.5, yMm: 46.8 });
    pdfPositions.push({ pageIndex: 2, text: issuePlace, size: 10, xMm: 57.5, yMm: 53.9 });
    pdfPositions.push({ pageIndex: 2, text: phoneNumber, size: 10, xMm: 57.5, yMm: 61.2 });
    pdfPositions.push({ pageIndex: 2, text: address, size: 10, xMm: 57.5, yMm: 68.4 });

    if (taxCode) {
      pdfPositions.push({ pageIndex: 2, text: taxCode, size: 10, xMm: 57.5, yMm: 75.8 });
    }

    pdfPositions.push({
      pageIndex: 15,
      text: name.toUpperCase(),
      size: 12,
      xMm: 136,
      yMm: 104.6,
      isCenter: true,
      fontWeight: 600
    });

    return pdfPositions;
  }

  /**
   * Tạo vị trí text cho hợp đồng affiliate của khách hàng doanh nghiệp
   * @param userId ID của người dùng
   * @param time Thời gian tạo hợp đồng (timestamp)
   * @param representativeName Tên người đại diện
   * @param representativePosition Chức vụ người đại diện
   * @param email Email
   * @param phoneNumber Số điện thoại
   * @param address Địa chỉ
   * @param taxCode Mã số thuế
   * @param businessName Tên doanh nghiệp
   * @returns Danh sách vị trí text
   */
  static affiliateContractBusiness(
    userId: number,
    time: number,
    representativeName: string,
    representativePosition: string,
    email: string,
    phoneNumber: string,
    address: string,
    taxCode: string,
    businessName: string,
  ): PdfPosition[] {
    const pdfPositions: PdfPosition[] = [];

    // Định dạng ngày/tháng/năm
    const formatter = (date: Date): string => {
      const day = date.getDate().toString().padStart(2, '0');
      const month = (date.getMonth() + 1).toString().padStart(2, '0');
      const year = date.getFullYear();
      return `${day}/${month}/${year}`;
    };

    // Chuyển đổi timestamp thành ngày
    const formattedTime = formatter(new Date(time));

    pdfPositions.push({ pageIndex: 1, text: `${userId}DN`, size: 12, xMm: 119, yMm: 87 });
    pdfPositions.push({ pageIndex: 1, text: formattedTime, size: 10, xMm: 49, yMm: 163 });
    pdfPositions.push({ pageIndex: 1, text: businessName, size: 12, xMm: 77, yMm: 257.5 });
    pdfPositions.push({ pageIndex: 1, text: representativeName, size: 10, xMm: 57.5, yMm: 265 });
    pdfPositions.push({ pageIndex: 2, text: representativePosition, size: 10, xMm: 57.5, yMm: 25 });
    pdfPositions.push({ pageIndex: 2, text: email, size: 10, xMm: 57.5, yMm: 32 });
    pdfPositions.push({ pageIndex: 2, text: phoneNumber, size: 10, xMm: 57.5, yMm: 39.5 });
    pdfPositions.push({ pageIndex: 2, text: address, size: 10, xMm: 57.5, yMm: 46.5 });
    pdfPositions.push({ pageIndex: 2, text: taxCode, size: 10, xMm: 57.5, yMm: 54 });
    pdfPositions.push({
      pageIndex: 14,
      text: representativeName.toUpperCase(),
      size: 12,
      xMm: 136,
      yMm: 268,
      isCenter: true
    });

    return pdfPositions;
  }

  /**
   * Tạo vị trí text cho hợp đồng nguyên tắc của khách hàng cá nhân
   * @param userId ID của người dùng
   * @param time Thời gian tạo hợp đồng (timestamp)
   * @param name Tên người dùng
   * @param dateOfBirth Ngày sinh
   * @param cccd Số CCCD
   * @param issueDate Ngày cấp CCCD
   * @param issuePlace Nơi cấp CCCD
   * @param phoneNumber Số điện thoại
   * @param address Địa chỉ
   * @param taxCode Mã số thuế
   * @returns Danh sách vị trí text
   */
  static ruleContractCustomer(
    userId: number,
    time: number,
    name: string,
    dateOfBirth: Date,
    cccd: string,
    issueDate: Date,
    issuePlace: string,
    phoneNumber: string,
    address: string,
    taxCode?: string,
  ): PdfPosition[] {
    const pdfPositions: PdfPosition[] = [];

    // Định dạng ngày/tháng/năm
    const formatter = (date: Date): string => {
      const day = date.getDate().toString().padStart(2, '0');
      const month = (date.getMonth() + 1).toString().padStart(2, '0');
      const year = date.getFullYear();
      return `${day}/${month}/${year}`;
    };

    // Chuyển đổi timestamp thành ngày
    const formattedTime = formatter(new Date(time));

    pdfPositions.push({ pageIndex: 1, text: `${userId}CN`, size: 12, xMm: 115, yMm: 83.9 });
    pdfPositions.push({ pageIndex: 1, text: formattedTime, size: 10, xMm: 49, yMm: 161 });
    pdfPositions.push({ pageIndex: 1, text: name, size: 12, xMm: 76, yMm: 168 });
    pdfPositions.push({ pageIndex: 1, text: formatter(dateOfBirth), size: 10, xMm: 57.5, yMm: 175 });
    pdfPositions.push({ pageIndex: 1, text: cccd, size: 10, xMm: 57.5, yMm: 182 });
    pdfPositions.push({ pageIndex: 1, text: formatter(issueDate), size: 10, xMm: 57.5, yMm: 190 });
    pdfPositions.push({ pageIndex: 1, text: issuePlace, size: 10, xMm: 57.5, yMm: 197 });
    pdfPositions.push({ pageIndex: 1, text: phoneNumber, size: 10, xMm: 57.5, yMm: 204 });
    pdfPositions.push({ pageIndex: 1, text: address, size: 10, xMm: 57.5, yMm: 211 });

    if (taxCode) {
      pdfPositions.push({ pageIndex: 1, text: taxCode, size: 10, xMm: 57.5, yMm: 219 });
    }

    pdfPositions.push({
      pageIndex: 13,
      text: name.toUpperCase(),
      size: 12,
      xMm: 60,
      yMm: 157,
      isCenter: true
    });

    return pdfPositions;
  }

  /**
   * Tạo vị trí text cho hợp đồng nguyên tắc của khách hàng doanh nghiệp
   * @param userId ID của người dùng
   * @param time Thời gian tạo hợp đồng (timestamp)
   * @param businessName Tên doanh nghiệp
   * @param representativeName Tên người đại diện
   * @param representativePosition Chức vụ người đại diện
   * @param businessEmail Email doanh nghiệp
   * @param businessPhone Số điện thoại doanh nghiệp
   * @param businessAddress Địa chỉ doanh nghiệp
   * @param taxCode Mã số thuế
   * @returns Danh sách vị trí text
   */
  static ruleContractBusiness(
    userId: number,
    time: number,
    businessName: string,
    representativeName: string,
    representativePosition: string,
    businessEmail: string,
    businessPhone: string,
    businessAddress: string,
    taxCode: string,
  ): PdfPosition[] {
    const pdfPositions: PdfPosition[] = [];

    // Định dạng ngày/tháng/năm
    const formatter = (date: Date): string => {
      const day = date.getDate().toString().padStart(2, '0');
      const month = (date.getMonth() + 1).toString().padStart(2, '0');
      const year = date.getFullYear();
      return `${day}/${month}/${year}`;
    };

    // Chuyển đổi timestamp thành ngày
    const formattedTime = formatter(new Date(time));

    pdfPositions.push({ pageIndex: 1, text: `${userId}DN`, size: 12, xMm: 116, yMm: 84 });
    pdfPositions.push({ pageIndex: 1, text: formattedTime, size: 10, xMm: 49, yMm: 160.4 });
    pdfPositions.push({ pageIndex: 1, text: businessName, size: 12, xMm: 75, yMm: 168 });
    pdfPositions.push({ pageIndex: 1, text: representativeName, size: 10, xMm: 57.5, yMm: 175 });
    pdfPositions.push({ pageIndex: 1, text: representativePosition, size: 10, xMm: 57.5, yMm: 183 });
    pdfPositions.push({ pageIndex: 1, text: businessEmail, size: 10, xMm: 57.5, yMm: 190 });
    pdfPositions.push({ pageIndex: 1, text: businessPhone, size: 10, xMm: 57.5, yMm: 197 });
    pdfPositions.push({ pageIndex: 1, text: businessAddress, size: 10, xMm: 57.5, yMm: 204 });
    pdfPositions.push({ pageIndex: 1, text: taxCode, size: 10, xMm: 57.5, yMm: 211 });
    pdfPositions.push({
      pageIndex: 13,
      text: representativeName.toUpperCase(),
      size: 12,
      xMm: 60,
      yMm: 116,
      isCenter: true
    });

    return pdfPositions;
  }
}