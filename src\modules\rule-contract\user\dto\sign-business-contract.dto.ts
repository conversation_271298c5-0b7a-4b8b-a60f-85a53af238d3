import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString, IsOptional, IsEnum } from 'class-validator';

/**
 * D<PERSON> cho yêu c<PERSON>u lấy URL upload chữ ký doanh nghiệp
 */
export class GetSignatureUploadUrlDto {
  /**
   * Loại file chữ ký
   */
  @ApiProperty({
    description: 'Loại file chữ ký',
    example: 'image/png',
    enum: ['image/png', 'image/jpeg', 'image/jpg'],
    required: true,
  })
  @IsNotEmpty({ message: 'Loại file không được để trống' })
  @IsString({ message: 'Loại file phải là chuỗi' })
  @IsEnum(['image/png', 'image/jpeg', 'image/jpg'], { 
    message: 'Loại file phải là image/png, image/jpeg hoặc image/jpg' 
  })
  fileType: 'image/png' | 'image/jpeg' | 'image/jpg';

  /**
   * <PERSON><PERSON>ch thước file (bytes) - tùy chọn
   */
  @ApiProperty({
    description: 'Kích thước file (bytes)',
    example: 1048576,
    required: false,
  })
  @IsOptional()
  fileSize?: number;
}

/**
 * DTO cho phản hồi URL upload chữ ký
 */
export class SignatureUploadUrlResponseDto {
  /**
   * URL tạm thời để upload chữ ký
   */
  @ApiProperty({
    description: 'URL tạm thời để upload chữ ký',
    example: 'https://s3.amazonaws.com/bucket/path/to/signature?X-Amz-Signature=...',
  })
  uploadUrl: string;

  /**
   * Key của file trên S3
   */
  @ApiProperty({
    description: 'Key của file trên S3',
    example: 'rule-contracts/signatures/business/user-123/signature-1234567890.png',
  })
  signatureKey: string;

  /**
   * Thời gian hết hạn của URL (giây)
   */
  @ApiProperty({
    description: 'Thời gian hết hạn của URL (giây)',
    example: 900,
  })
  expiresIn: number;
}

/**
 * DTO cho yêu cầu ký hợp đồng doanh nghiệp
 */
export class SignBusinessContractDto {
  /**
   * Key của file chữ ký đã upload lên S3
   */
  @ApiProperty({
    description: 'Key của file chữ ký đã upload lên S3',
    example: 'rule-contracts/signatures/business/user-123/signature-1234567890.png',
    required: true,
  })
  @IsNotEmpty({ message: 'Key chữ ký không được để trống' })
  @IsString({ message: 'Key chữ ký phải là chuỗi' })
  signatureKey: string;

  /**
   * Tên người ký (tùy chọn)
   */
  @ApiProperty({
    description: 'Tên người ký',
    example: 'Nguyễn Văn A',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Tên người ký phải là chuỗi' })
  signerName?: string;

  /**
   * Chức vụ người ký (tùy chọn)
   */
  @ApiProperty({
    description: 'Chức vụ người ký',
    example: 'Giám đốc',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Chức vụ người ký phải là chuỗi' })
  signerPosition?: string;
}

/**
 * DTO cho phản hồi ký hợp đồng thành công
 */
export class SignContractResponseDto {
  /**
   * Trạng thái mới của hợp đồng
   */
  @ApiProperty({
    description: 'Trạng thái mới của hợp đồng',
    example: 'pendingApproval',
  })
  state: string;

  /**
   * Thời gian ký hợp đồng
   */
  @ApiProperty({
    description: 'Thời gian ký hợp đồng (timestamp)',
    example: 1625097600000,
  })
  signedAt: number;

  /**
   * URL xem hợp đồng đã ký
   */
  @ApiProperty({
    description: 'URL xem hợp đồng đã ký',
    example: 'https://cdn.example.com/contracts/signed-contract.pdf',
  })
  signedContractUrl: string;
}
