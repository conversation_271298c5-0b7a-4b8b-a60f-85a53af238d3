// Simple test to verify the validator works
const { isValidPhoneNumber } = require('libphonenumber-js');

console.log('Testing libphonenumber-js...');

const testPhones = [
  '+84912345678',
  '+1234567890',
  '+447911123456',
  '84912345678', // Invalid - missing +
  'invalid',
  '+84123' // Too short
];

testPhones.forEach(phone => {
  try {
    const isValid = isValidPhoneNumber(phone);
    console.log(`${phone}: ${isValid ? '✅ Valid' : '❌ Invalid'}`);
  } catch (error) {
    console.log(`${phone}: ❌ Error - ${error.message}`);
  }
});

console.log('\nValidator library is working correctly!');
