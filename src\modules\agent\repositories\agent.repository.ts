import { Injectable, Logger } from '@nestjs/common';
import { DataSource, Repository, SelectQueryBuilder } from 'typeorm';
import { Agent } from '@modules/agent/entities';
import { AgentStatusEnum } from '@modules/agent/constants/agent-status.enum';
import { Transactional } from 'typeorm-transactional';
import { PaginatedResult } from '@common/response';
import { AgentSimpleQueryDto } from '@modules/agent/user/dto';

/**
 * Repository cho Agent
 * Xử lý các thao tác CRUD và truy vấn nâng cao liên quan đến agent
 */
@Injectable()
export class AgentRepository extends Repository<Agent> {
  private readonly logger = new Logger(AgentRepository.name);

  constructor(private dataSource: DataSource) {
    super(Agent, dataSource.createEntityManager());
  }

  /**
   * Tạo query builder cơ bản cho Agent
   * @returns SelectQueryBuilder cho Agent
   */
  createBaseQuery(): SelectQueryBuilder<Agent> {
    return this.createQueryBuilder('agent');
  }

  /**
   * <PERSON><PERSON><PERSON> tra sự tồn tại của agent theo ID
   * @param id ID của agent
   * @returns true nếu agent tồn tại, false nếu không tồn tại
   */
  async existsById(id: string): Promise<boolean> {
    if (!id) return false;

    const count = await this.createBaseQuery()
      .select('1')
      .where('agent.id = :id', { id })
      .andWhere('agent.deletedAt IS NULL')
      .limit(1)
      .getCount();

    this.logger.debug(`Kiểm tra tồn tại agent với ID ${id}: ${count > 0}`);
    return count > 0;
  }

  /**
   * Tìm agent theo ID
   * @param id ID của agent
   * @returns Agent nếu tìm thấy, null nếu không tìm thấy
   */
  async findById(id: string): Promise<Agent | null> {
    return this.createBaseQuery()
      .where('agent.id = :id', { id })
      .andWhere('agent.deletedAt IS NULL')
      .getOne();
  }

  /**
   * Tìm agent theo ID và ID của người dùng
   * @param id ID của agent
   * @param userId ID của người dùng
   * @returns Agent nếu tìm thấy, null nếu không tìm thấy
   */
  async findOneByIdAndUserId(id: string, userId: number): Promise<Agent | null> {
    return this.createBaseQuery()
      .innerJoin('agents_user', 'agentUser', 'agentUser.id = agent.id')
      .where('agent.id = :id', { id })
      .andWhere('agentUser.user_id = :userId', { userId })
      .andWhere('agent.deleted_at IS NULL')
      .getOne();
  }

  /**
   * Tìm agent theo tên
   * @param name Tên của agent
   * @returns Agent nếu tìm thấy, null nếu không tìm thấy
   */
  async findByName(name: string): Promise<Agent | null> {
    return this.createBaseQuery()
      .where('agent.name = :name', { name })
      .andWhere('agent.deletedAt IS NULL')
      .getOne();
  }

  /**
   * Kiểm tra tên agent đã tồn tại cho user cụ thể chưa
   * @param name Tên của agent
   * @param userId ID của người dùng
   * @returns true nếu tên đã tồn tại, false nếu chưa tồn tại
   */
  async existsByNameAndUserId(name: string, userId: number): Promise<boolean> {
    if (!name || !userId) return false;

    const count = await this.createBaseQuery()
      .select('1')
      .innerJoin('agents_user', 'agentUser', 'agentUser.id = agent.id')
      .where('agent.name = :name', { name })
      .andWhere('agentUser.user_id = :userId', { userId })
      .andWhere('agent.deleted_at IS NULL') // Loại bỏ các agent đã xóa mềm
      .limit(1)
      .getCount();

    this.logger.debug(`Kiểm tra tồn tại tên agent '${name}' cho user ${userId}: ${count > 0}`);
    return count > 0;
  }

  /**
   * Kiểm tra agent có tồn tại và thuộc về user cụ thể không
   * @param id ID của agent
   * @param userId ID của người dùng
   * @returns true nếu agent tồn tại và thuộc về user, false nếu không
   */
  async existsByIdAndUserId(id: string, userId: number): Promise<boolean> {
    if (!id || !userId) return false;

    const count = await this.createBaseQuery()
      .select('1')
      .innerJoin('agents_user', 'agentUser', 'agentUser.id = agent.id')
      .where('agent.id = :id', { id })
      .andWhere('agentUser.user_id = :userId', { userId })
      .andWhere('agent.deleted_at IS NULL') // Loại bỏ các agent đã xóa mềm
      .limit(1)
      .getCount();

    this.logger.debug(`Kiểm tra tồn tại agent '${id}' cho user ${userId}: ${count > 0}`);
    return count > 0;
  }

  /**
   * Tìm nhiều agent theo danh sách ID, chỉ lấy các trường cần thiết
   * @param ids Danh sách ID của agent
   * @returns Danh sách agent tìm thấy
   */
  async findByIds(ids: string[]): Promise<Agent[]> {
    if (!ids || ids.length === 0) {
      return [];
    }

    return this.createBaseQuery()
      .select([
        'agent.id',
        'agent.name',
        'agent.avatar',
        'agent.modelConfig',
      ])
      .where('agent.id IN (:...ids)', { ids })
      .andWhere('agent.deletedAt IS NULL')
      .getMany();
  }

  /**
   * Tìm danh sách agent với phân trang
   * @param page Trang hiện tại
   * @param limit Số lượng item trên một trang
   * @param search Từ khóa tìm kiếm (tìm theo tên)
   * @param status Trạng thái của agent
   * @param sortBy Trường sắp xếp
   * @param sortDirection Hướng sắp xếp
   * @returns Danh sách agent với phân trang
   */
  async findPaginated(
    page: number,
    limit: number,
    search?: string,
    sortBy: string = 'createdAt',
    sortDirection: 'ASC' | 'DESC' = 'DESC',
  ): Promise<{ items: Agent[]; total: number }> {
    const qb = this.createBaseQuery();

    // Thêm điều kiện tìm kiếm nếu có
    if (search) {
      qb.andWhere('agent.name ILIKE :search', { search: `%${search}%` });
    }

    // Loại bỏ các bản ghi đã xóa mềm
    qb.andWhere('agent.deletedAt IS NULL');

    // Thêm phân trang và sắp xếp
    qb.skip((page - 1) * limit)
      .take(limit)
      .orderBy(`agent.${sortBy}`, sortDirection);

    // Lấy kết quả và tổng số lượng
    const [items, total] = await qb.getManyAndCount();

    return { items, total };
  }

  /**
   * Tìm danh sách agent đã xóa với phân trang
   * @param page Trang hiện tại
   * @param limit Số lượng item trên một trang
   * @param search Từ khóa tìm kiếm (tìm theo tên)
   * @param status Trạng thái của agent
   * @param sortBy Trường sắp xếp
   * @param sortDirection Hướng sắp xếp
   * @returns Danh sách agent đã xóa với phân trang
   */
  async findDeletedWithPagination(
    page: number,
    limit: number,
    search?: string,
    status?: AgentStatusEnum,
    sortBy: string = 'deletedAt',
    sortDirection: 'ASC' | 'DESC' = 'DESC',
  ): Promise<{ items: Agent[]; total: number }> {
    const qb = this.createBaseQuery()
      .withDeleted() // Bao gồm cả các bản ghi đã xóa mềm
      .where('agent.deletedAt IS NOT NULL'); // Chỉ lấy các bản ghi đã xóa mềm

    // Thêm điều kiện tìm kiếm nếu có
    if (search) {
      qb.andWhere('agent.name ILIKE :search', { search: `%${search}%` });
    }

    // Thêm điều kiện lọc theo trạng thái nếu có
    if (status) {
      qb.andWhere('agent.status = :status', { status });
    }

    // Thêm phân trang và sắp xếp
    qb.skip((page - 1) * limit)
      .take(limit)
      .orderBy(`agent.${sortBy}`, sortDirection);

    // Lấy kết quả và tổng số lượng
    const [items, total] = await qb.getManyAndCount();

    return { items, total };
  }

  /**
   * Tìm agent đã xóa theo ID
   * @param id ID của agent
   * @returns Agent nếu tìm thấy, null nếu không tìm thấy
   */
  async findDeletedById(id: string): Promise<Agent | null> {
    return this.createBaseQuery()
      .withDeleted() // Bao gồm cả các bản ghi đã xóa mềm
      .where('agent.id = :id', { id })
      .andWhere('agent.deletedAt IS NOT NULL') // Chỉ lấy các bản ghi đã xóa mềm
      .getOne();
  }

  /**
   * Kiểm tra agent có tồn tại không (bao gồm cả đã xóa)
   * @param id ID của agent
   * @returns true nếu tồn tại, false nếu không
   */
  async existsByIdIncludingDeleted(id: string): Promise<boolean> {
    const count = await this.createQueryBuilder('agent')
      .where('agent.id = :id', { id })
      .withDeleted() // Bao gồm cả records đã bị soft delete
      .getCount();

    return count > 0;
  }

  /**
   * Khôi phục agent đã xóa
   * @param ids Danh sách ID của các agent cần khôi phục
   * @returns Số lượng bản ghi đã được khôi phục
   */
  async restoreAgents(ids: string[]): Promise<number> {
    if (!ids || ids.length === 0) {
      return 0;
    }

    // Khôi phục các agent đã xóa
    const result = await this.createQueryBuilder()
      .update(Agent)
      .set({ deletedAt: null })
      .where('id IN (:...ids)', { ids })
      .andWhere('deletedAt IS NOT NULL')
      .execute();

    return result.affected || 0;
  }

  /**
   * Lấy danh sách agent đơn giản theo userId (chỉ id, avatar, name)
   * @param userId ID của người dùng
   * @returns Danh sách agent đơn giản
   */
  async findSimpleListByUserId(userId: number): Promise<{ id: string; avatar: string | null; name: string }[]> {
    return this.createBaseQuery()
      .select([
        'agent.id',
        'agent.avatar',
        'agent.name'
      ])
      .innerJoin('agents_user', 'agentUser', 'agentUser.id = agent.id')
      .where('agentUser.user_id = :userId', { userId })
      .andWhere('agent.deleted_at IS NULL') // Loại bỏ các agent đã xóa mềm
      .orderBy('agent.created_at', 'DESC') // Sắp xếp theo thời gian tạo mới nhất
      .getMany();
  }

  /**
   * Lấy danh sách agent đơn giản theo userId với phân trang (chỉ id, avatar, name)
   * @param userId ID của người dùng
   * @param queryDto Tham số truy vấn với phân trang
   * @returns Danh sách agent đơn giản có phân trang
   */
  async findSimpleListByUserIdPaginated(
    userId: number,
    queryDto: AgentSimpleQueryDto
  ): Promise<PaginatedResult<{ id: string; avatar: string | null; name: string }>> {
    const { page = 1, limit = 10, search, sortBy = 'createdAt', sortDirection = 'DESC' } = queryDto;

    // Tạo query builder
    const queryBuilder = this.createBaseQuery()
      .select([
        'agent.id',
        'agent.avatar',
        'agent.name',
        'agent.created_at'
      ])
      .innerJoin('agents_user', 'agentUser', 'agentUser.id = agent.id')
      .where('agentUser.user_id = :userId', { userId })
      .andWhere('agent.deleted_at IS NULL'); // Loại bỏ các agent đã xóa mềm

    // Thêm điều kiện tìm kiếm nếu có
    if (search) {
      queryBuilder.andWhere('agent.name ILIKE :search', { search: `%${search}%` });
    }

    // Thêm sắp xếp
    const sortColumn = sortBy === 'name' ? 'agent.name' : 'agent.created_at';
    const direction = sortDirection?.toUpperCase() === 'ASC' ? 'ASC' : 'DESC';
    queryBuilder.orderBy(sortColumn, direction as 'ASC' | 'DESC');

    // Thêm phân trang
    const skip = (page - 1) * limit;
    queryBuilder.offset(skip).limit(limit);

    // Thực hiện truy vấn
    const [items, totalItems] = await Promise.all([
      queryBuilder.getMany(),
      queryBuilder.getCount(),
    ]);

    return {
      items,
      meta: {
        totalItems,
        itemCount: items.length,
        itemsPerPage: limit,
        totalPages: Math.ceil(totalItems / limit),
        currentPage: page,
      },
    };
  }

  /**
   * Xóa mềm agent (cập nhật trường deletedAt)
   * @param id ID của agent cần xóa mềm
   * @returns true nếu xóa thành công, false nếu không
   */
  @Transactional()
  async customSoftDelete(id: string): Promise<boolean> {
    try {
      this.logger.debug(`Thực hiện xóa mềm agent với ID: ${id}`);

      // Cập nhật trường deletedAt
      const result = await this.createQueryBuilder()
        .update(Agent)
        .set({
          deletedAt: Date.now()
        })
        .where('id = :id', { id })
        .andWhere('deletedAt IS NULL')
        .execute();

      const success = result.affected !== null && result.affected !== undefined && result.affected > 0;
      this.logger.debug(`Kết quả xóa mềm agent: ${success ? 'Thành công' : 'Thất bại'}`);

      return success;
    } catch (error) {
      this.logger.error(`Lỗi khi xóa mềm agent: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Bulk soft delete agents (cập nhật trường deletedAt)
   * @param ids Danh sách IDs cần xóa mềm
   * @returns Số lượng records đã được cập nhật
   */
  async bulkSoftDelete(ids: string[]): Promise<number> {
    if (!ids || ids.length === 0) {
      return 0;
    }

    const currentTimestamp = Date.now();
    const result = await this.createQueryBuilder()
      .update(Agent)
      .set({ deletedAt: currentTimestamp })
      .where('id IN (:...ids)', { ids })
      .andWhere('deletedAt IS NULL')
      .execute();

    return result.affected || 0;
  }

  /**
   * Bulk restore agents (set deletedAt = null)
   * @param ids Danh sách IDs cần khôi phục
   * @returns Số lượng records đã được khôi phục
   */
  async bulkRestore(ids: string[]): Promise<number> {
    if (!ids || ids.length === 0) {
      return 0;
    }

    const result = await this.createQueryBuilder()
      .update(Agent)
      .set({ deletedAt: null })
      .where('id IN (:...ids)', { ids })
      .andWhere('deletedAt IS NOT NULL')
      .execute();

    return result.affected || 0;
  }

  /**
   * Cập nhật Vector Store ID cho agent
   * @param agentId ID của agent
   * @param vectorStoreId ID của vector store
   */
  async updateVectorStoreId(agentId: string, vectorStoreId: string): Promise<void> {
    try {
      const result = await this.createQueryBuilder()
        .update(Agent)
        .set({ vectorStoreId })
        .where('id = :agentId', { agentId })
        .andWhere('deleted_at IS NULL')
        .execute();

      if (result.affected === 0) {
        this.logger.warn(`Không tìm thấy agent ${agentId} để cập nhật vector store`);
      }
    } catch (error) {
      this.logger.error(`Lỗi khi cập nhật vector store cho agent ${agentId}: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Cập nhật avatar cho agent
   * @param agentId ID của agent
   * @param avatarKey S3 key của avatar
   */
  async updateAvatar(agentId: string, avatarKey: string): Promise<void> {
    try {
      const result = await this.createQueryBuilder()
        .update(Agent)
        .set({ avatar: avatarKey })
        .where('id = :agentId', { agentId })
        .andWhere('deleted_at IS NULL')
        .execute();

      if (result.affected === 0) {
        this.logger.warn(`Không tìm thấy agent ${agentId} để cập nhật avatar`);
      }
    } catch (error) {
      this.logger.error(`Lỗi khi cập nhật avatar cho agent ${agentId}: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Cập nhật thông tin agent
   * @param agentId ID của agent
   * @param updateData Dữ liệu cập nhật
   */
  async updateAgent(agentId: string, updateData: Partial<Agent>): Promise<void> {
    try {
      // Loại bỏ các trường không được phép cập nhật
      const { id, createdAt, deletedAt, ...allowedData } = updateData;

      // Thêm updatedAt
      const dataToUpdate = {
        ...allowedData,
        updatedAt: Date.now()
      };

      const result = await this.createQueryBuilder()
        .update(Agent)
        .set(dataToUpdate)
        .where('id = :agentId', { agentId })
        .andWhere('deleted_at IS NULL')
        .execute();

      if (result.affected === 0) {
        this.logger.warn(`Không tìm thấy agent ${agentId} để cập nhật`);
      }

      this.logger.debug(`Đã cập nhật agent ${agentId}:`, dataToUpdate);
    } catch (error) {
      this.logger.error(`Lỗi khi cập nhật agent ${agentId}: ${error.message}`, error.stack);
      throw error;
    }
  }
}
