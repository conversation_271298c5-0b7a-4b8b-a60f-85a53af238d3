import { ContractStatusEnum, ContractTypeEnum } from '../entities/rule-contract.entity';
import { PdfPosition } from '@/shared/interface/pdf-edit.interface';

/**
 * Các trạng thái của máy trạng thái hợp đồng nguyên tắc
 */
export enum RuleContractState {
  DRAFT = 'draft',
  PENDING_APPROVAL = 'pendingApproval',
  APPROVED = 'approved',
  REJECTED = 'rejected',
}

/**
 * Các sự kiện có thể xảy ra trong quy trình hợp đồng nguyên tắc
 */
export enum RuleContractEvent {
  CREATE = 'CREATE',
  SIGN_BY_USER = 'SIGN_BY_USER',
  APPROVE = 'APPROVE',
  REJECT = 'REJECT',
  RESUBMIT = 'RESUBMIT',
  UPGRADE_TO_BUSINESS = 'UPGRADE_TO_BUSINESS',
}

/**
 * <PERSON>ữ cảnh của máy trạng thái hợp đồng nguyên tắc
 */
export interface RuleContractContext {
  // Thông tin cơ bản
  contractId: number | null;
  userId: number | null;
  adminId?: number | null;

  // Thông tin hợp đồng
  contractType: ContractTypeEnum;
  contractStatus: ContractStatusEnum;
  contractKey?: string | null;

  // Thông tin chữ ký
  signatureData?: string | null;
  userSignatureAt?: number | null;
  adminSignatureAt?: number | null;

  // Thông tin từ chối
  rejectionReason?: string | null;

  // Thời gian
  createdAt?: number;
  updatedAt?: number;
}

/**
 * Dữ liệu đi kèm với sự kiện CREATE
 */
export interface CreateEventData {
  userId: number;
  contractType: ContractTypeEnum;
  contractKey?: string;
  individualContractData?: IndividualContractData;
  businessContractData?: BusinessContractData;
}

/**
 * Dữ liệu cho hợp đồng cá nhân
 */
export interface IndividualContractData {
  name: string;
  address: string;
  phone: string;
  dateOfBirth: string;
  cccd: string;
  issuePlace: string;
  issueDate: string;
  taxCode?: string;
  positions?: PdfPosition[];
}

/**
 * Dữ liệu cho hợp đồng doanh nghiệp
 */
export interface BusinessContractData {
  businessName: string;
  representativeName: string;
  representativePosition: string;
  businessEmail: string;
  businessPhone: string;
  businessAddress: string;
  taxCode: string;
  positions?: PdfPosition[];
}

/**
 * Dữ liệu đi kèm với sự kiện SIGN_BY_USER
 */
export interface SignByUserEventData {
  signatureData: string;
  contractType?: ContractTypeEnum;
  otpToken?: string;
  otpCode?: string;
  signatureKey?: string;
  signerName?: string;
  signerPosition?: string;
}

/**
 * Dữ liệu đi kèm với sự kiện APPROVE
 */
export interface ApproveEventData {
  adminId: number;
}

/**
 * Dữ liệu đi kèm với sự kiện REJECT
 */
export interface RejectEventData {
  adminId: number;
  rejectionReason: string;
}

/**
 * Dữ liệu đi kèm với sự kiện UPGRADE_TO_BUSINESS
 */
export interface UpgradeToBusinessEventData {
  newContractKey?: string;
}

/**
 * Map trạng thái từ RuleContractState sang ContractStatusEnum
 * @param state Trạng thái trong máy trạng thái
 * @returns Trạng thái tương ứng trong enum
 */
export function mapStateToStatus(state: RuleContractState): ContractStatusEnum {
  switch (state) {
    case RuleContractState.DRAFT:
      return ContractStatusEnum.DRAFT;
    case RuleContractState.PENDING_APPROVAL:
      return ContractStatusEnum.PENDING_APPROVAL;
    case RuleContractState.APPROVED:
      return ContractStatusEnum.APPROVED;
    case RuleContractState.REJECTED:
      return ContractStatusEnum.REJECTED;
    default:
      return ContractStatusEnum.DRAFT;
  }
}

/**
 * Map trạng thái từ ContractStatusEnum sang RuleContractState
 * @param status Trạng thái trong enum
 * @returns Trạng thái tương ứng trong máy trạng thái
 */
export function mapStatusToState(status: ContractStatusEnum): RuleContractState {
  switch (status) {
    case ContractStatusEnum.DRAFT:
      return RuleContractState.DRAFT;
    case ContractStatusEnum.PENDING_APPROVAL:
      return RuleContractState.PENDING_APPROVAL;
    case ContractStatusEnum.APPROVED:
      return RuleContractState.APPROVED;
    case ContractStatusEnum.REJECTED:
      return RuleContractState.REJECTED;
    default:
      return RuleContractState.DRAFT;
  }
}
