import { Injectable, Logger } from '@nestjs/common';
import { AppException, ErrorCode } from '@/common';
import {
  QueueName,
  EmailJobName,
  SmsJobName,
  NotificationJobName,
  EmailSystemJobName,
  CrawlUrlJobName,
  DEFAULT_JOB_OPTIONS,
  HIGH_PRIORITY_JOB_OPTIONS
} from './queue.constants';
import { EmailJobData, TemplateEmailJobData, JobOptions, SmsSystemJobData } from './queue.types';
import { EmailSystemJobDto } from './email-system-queue.service';
import { InjectQueue } from '@nestjs/bullmq';
import { Queue } from 'bullmq';

/**
 * Job names for system email queue
 */
export enum SystemEmailJobName {
  SEND_SYSTEM_EMAIL = 'send-system-email',
  SEND_TEMPLATE_EMAIL = 'send-template-email'
}

/**
 * Service quản lý việc thêm job vào các queue
 */
@Injectable()
export class QueueService {
  private readonly logger = new Logger(QueueService.name);

  constructor(
    @InjectQueue(QueueName.EMAIL) private readonly emailQueue: Queue,
    @InjectQueue(QueueName.SMS) private readonly smsQueue: Queue,
    @InjectQueue(QueueName.NOTIFICATION) private readonly notificationQueue: Queue,
    @InjectQueue(QueueName.DATA_PROCESS) private readonly dataProcessQueue: Queue,
    @InjectQueue(QueueName.SEND_SYSTEM_EMAIL) private readonly systemEmailQueue: Queue,
    @InjectQueue(QueueName.AGENT) private readonly agentQueue: Queue,
    @InjectQueue(QueueName.EMAIL_SYSTEM) private readonly emailSystemQueue: Queue,
    @InjectQueue(QueueName.CRAWL_URL) private readonly crawlUrlQueue: Queue,
    @InjectQueue(QueueName.CRAWL_URL_ADMIN) private readonly crawlUrlAdminQueue: Queue,
  ) {}



  /**
   * Thêm job vào queue email
   * @param data Dữ liệu email cần gửi
   * @param opts Tùy chọn job
   * @returns Promise với ID của job đã tạo
   */
  async addEmailJob(data: EmailJobData, opts?: JobOptions): Promise<string | undefined> {
    try {
      const job = await this.emailQueue.add(EmailJobName.SEND_EMAIL, data, opts);
      this.logger.log(`Đã thêm job email vào queue: ${job.id}`);
      return job.id;
    } catch (error) {
      this.logger.error(`Lỗi khi thêm job email vào queue: ${error.message}`, error.stack);
      throw new AppException(ErrorCode.INTERNAL_SERVER_ERROR, 'Không thể thêm job vào queue');
    }
  }

  /**
   * Thêm job gửi email theo mẫu vào queue
   * @param data Dữ liệu email mẫu cần gửi
   * @param opts Tùy chọn job
   * @returns Promise với ID của job đã tạo
   */
  async addTemplateEmailJob(data: TemplateEmailJobData, opts?: JobOptions): Promise<string | undefined> {
    try {
      const options = opts || HIGH_PRIORITY_JOB_OPTIONS;
      const job = await this.emailQueue.add(EmailJobName.SEND_TEMPLATE_EMAIL, data, options);
      this.logger.log(`Đã thêm job email mẫu vào queue: ${job.id}`);
      return job.id;
    } catch (error) {
      this.logger.error(`Lỗi khi thêm job email mẫu vào queue: ${error.message}`, error.stack);
      throw new AppException(ErrorCode.INTERNAL_SERVER_ERROR, 'Không thể thêm job vào queue');
    }
  }

  /**
   * Thêm job gửi email hệ thống vào queue
   * Sử dụng cấu hình email mặc định từ biến môi trường
   * @param data Dữ liệu email cần gửi
   * @param opts Tùy chọn job
   * @returns Promise với ID của job đã tạo
   */
  async addSystemEmailJob(data: EmailJobData, opts?: JobOptions): Promise<string | undefined> {
    try {
      const options = opts || DEFAULT_JOB_OPTIONS;
      const job = await this.systemEmailQueue.add(SystemEmailJobName.SEND_SYSTEM_EMAIL, data, options);
      this.logger.log(`Đã thêm job email hệ thống vào queue: ${job.id}`);
      return job.id;
    } catch (error) {
      this.logger.error(`Lỗi khi thêm job email hệ thống vào queue: ${error.message}`, error.stack);
      throw new AppException(ErrorCode.INTERNAL_SERVER_ERROR, 'Không thể thêm job email hệ thống vào queue');
    }
  }

  /**
   * Thêm job gửi email hệ thống theo mẫu vào queue
   * Sử dụng cấu hình email mặc định từ biến môi trường
   * @param data Dữ liệu email mẫu cần gửi
   * @param opts Tùy chọn job
   * @returns Promise với ID của job đã tạo
   */
  async addSystemTemplateEmailJob(data: TemplateEmailJobData, opts?: JobOptions): Promise<string | undefined> {
    try {
      const options = opts || HIGH_PRIORITY_JOB_OPTIONS;
      const job = await this.systemEmailQueue.add(SystemEmailJobName.SEND_TEMPLATE_EMAIL, data, options);
      this.logger.log(`Đã thêm job email mẫu hệ thống vào queue: ${job.id}`);
      return job.id;
    } catch (error) {
      this.logger.error(`Lỗi khi thêm job email mẫu hệ thống vào queue: ${error.message}`, error.stack);
      throw new AppException(ErrorCode.INTERNAL_SERVER_ERROR, 'Không thể thêm job email mẫu hệ thống vào queue');
    }
  }

  /**
   * Thêm job vào queue EMAIL_SYSTEM
   * @param data Dữ liệu EmailSystemJobDto
   * @param opts Tùy chọn job
   * @returns Promise với ID của job đã tạo
   */
  async addEmailSystemJob(data: EmailSystemJobDto, opts?: JobOptions): Promise<string | undefined> {
    try {
      const options = opts || HIGH_PRIORITY_JOB_OPTIONS;
      const job = await this.emailSystemQueue.add(EmailSystemJobName.SEND_TEMPLATE_EMAIL, data, options);
      this.logger.log(`Đã thêm job email system vào queue: ${job.id} - Category: ${data.category} - To: ${data.to}`);
      return job.id;
    } catch (error) {
      this.logger.error(`Lỗi khi thêm job email system vào queue: ${error.message}`, error.stack);
      throw new AppException(ErrorCode.INTERNAL_SERVER_ERROR, 'Không thể thêm job email system vào queue');
    }
  }

  /**
   * Thêm job vào queue SMS
   * @param data Dữ liệu job
   * @param opts Tùy chọn job
   * @returns Promise với ID của job đã tạo
   */
  async addSmsJob(data: any, opts?: JobOptions): Promise<string | undefined> {
    try {
      const job = await this.smsQueue.add(SmsJobName.SEND_SMS, data, opts);
      this.logger.log(`Đã thêm job SMS vào queue: ${job.id}`);
      return job.id;
    } catch (error) {
      this.logger.error(`Lỗi khi thêm job SMS vào queue: ${error.message}`, error.stack);
      throw new AppException(ErrorCode.INTERNAL_SERVER_ERROR, 'Không thể thêm job vào queue');
    }
  }

  /**
   * Thêm job gửi SMS hệ thống vào queue
   * @param jobData Dữ liệu job SMS hệ thống
   * @param opts Tùy chọn job
   * @returns Promise với ID của job đã tạo
   */
  async sendSmsSystemJob(
    jobData: Omit<SmsSystemJobData, 'timestamp'>,
    opts?: JobOptions
  ): Promise<string | undefined> {
    try {
      const smsJobData: SmsSystemJobData = {
        ...jobData,
        timestamp: Date.now(),
      };

      const job = await this.smsQueue.add(SmsJobName.SMS_SYSTEM, smsJobData, {
        ...DEFAULT_JOB_OPTIONS,
        ...opts,
      });

      this.logger.log(
        `Đã thêm job SMS hệ thống vào queue: ${job.id} - Type: ${jobData.type}${
          jobData.userId ? ` cho user ${jobData.userId}` : ''
        }`
      );
      return job.id;
    } catch (error) {
      this.logger.error(
        `Lỗi khi thêm job SMS hệ thống vào queue: ${error.message}`,
        error.stack
      );
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        'Không thể thêm job SMS hệ thống vào queue'
      );
    }
  }

  /**
   * Thêm job vào queue thông báo
   * @param data Dữ liệu job
   * @param opts Tùy chọn job
   * @returns Promise với ID của job đã tạo
   */
  async addNotificationJob(data: any, opts?: JobOptions): Promise<string | undefined> {
    try {
      const job = await this.notificationQueue.add(NotificationJobName.SEND_NOTIFICATION, data, opts);
      this.logger.log(`Đã thêm job thông báo vào queue: ${job.id}`);
      return job.id;
    } catch (error) {
      this.logger.error(`Lỗi khi thêm job thông báo vào queue: ${error.message}`, error.stack);
      throw new AppException(ErrorCode.INTERNAL_SERVER_ERROR, 'Không thể thêm job vào queue');
    }
  }

  /**
   * Thêm job vào queue xử lý dữ liệu
   * @param jobName Tên job cần xử lý
   * @param data Dữ liệu job
   * @param opts Tùy chọn job
   * @returns Promise với ID của job đã tạo
   */
  async addDataProcessJob(jobName: string, data: any, opts?: JobOptions): Promise<string | undefined> {
    try {
      const job = await this.dataProcessQueue.add(jobName, data, opts);
      this.logger.log(`Đã thêm job xử lý dữ liệu vào queue: ${job.id}`);
      return job.id;
    } catch (error) {
      this.logger.error(`Lỗi khi thêm job xử lý dữ liệu vào queue: ${error.message}`, error.stack);
      throw new AppException(ErrorCode.INTERNAL_SERVER_ERROR, 'Không thể thêm job vào queue');
    }
  }

  /**
   * Thêm job crawl URL cho user vào queue
   * @param data Dữ liệu crawl job
   * @param opts Tùy chọn job
   * @returns Promise với ID của job đã tạo
   */
  async addCrawlUrlJob(data: any, opts?: JobOptions): Promise<string | undefined> {
    try {
      // ✅ Đợi Redis connection sẵn sàng trước khi thêm job
      await this.ensureQueueConnectionReady(this.crawlUrlQueue, 'CRAWL_URL');

      const options = {
        ...(opts || DEFAULT_JOB_OPTIONS),
        jobId: data.sessionId // ✅ Sử dụng sessionId làm jobId
      };
      this.logger.log(`🔧 Adding user job to queue: ${QueueName.CRAWL_URL} with job name: ${CrawlUrlJobName.CRAWL_URL}`);
      this.logger.log(`🔧 User job data: ${JSON.stringify(data)}`);
      this.logger.log(`🔧 Using sessionId as jobId: ${data.sessionId}`);
      const job = await this.crawlUrlQueue.add(CrawlUrlJobName.CRAWL_URL, data, options);
      this.logger.log(`Đã thêm job crawl URL vào queue: ${job.id}`);

      // ✅ Thêm monitoring ngay sau khi add job
      this.monitorJobAfterAdd(job.id as string, 'USER');

      return job.id ? String(job.id) : undefined;
    } catch (error) {
      this.logger.error(`Lỗi khi thêm job crawl URL vào queue: ${error.message}`, error.stack);
      throw new AppException(ErrorCode.INTERNAL_SERVER_ERROR, 'Không thể thêm job crawl URL vào queue');
    }
  }

  /**
   * Thêm job crawl URL cho admin vào queue
   * @param data Dữ liệu crawl job
   * @param opts Tùy chọn job
   * @returns Promise với ID của job đã tạo
   */
  async addCrawlUrlAdminJob(data: any, opts?: JobOptions): Promise<string | undefined> {
    try {
      // ✅ Đợi Redis connection sẵn sàng trước khi thêm job admin
      await this.ensureQueueConnectionReady(this.crawlUrlAdminQueue, 'CRAWL_URL_ADMIN');

      const options = {
        ...(opts || DEFAULT_JOB_OPTIONS),
        jobId: data.sessionId // ✅ Sử dụng sessionId làm jobId cho admin
      };
      this.logger.log(`🔧 Adding admin job to queue: ${QueueName.CRAWL_URL_ADMIN} with job name: ${CrawlUrlJobName.CRAWL_URL_ADMIN}`);
      this.logger.log(`🔧 Admin job data: ${JSON.stringify(data)}`);
      this.logger.log(`🔧 Using sessionId as admin jobId: ${data.sessionId}`);
      const job = await this.crawlUrlAdminQueue.add(CrawlUrlJobName.CRAWL_URL_ADMIN, data, options);
      this.logger.log(`Đã thêm job crawl URL admin vào queue: ${job.id}`);

      // ✅ Thêm monitoring ngay sau khi add admin job
      this.monitorJobAfterAdd(job.id as string, 'ADMIN');

      return job.id ? String(job.id) : undefined;
    } catch (error) {
      this.logger.error(`Lỗi khi thêm job crawl URL admin vào queue: ${error.message}`, error.stack);
      throw new AppException(ErrorCode.INTERNAL_SERVER_ERROR, 'Không thể thêm job crawl URL admin vào queue');
    }
  }

  /**
   * Kiểm tra trạng thái của job
   * @param queueName Tên queue
   * @param jobId ID của job
   * @returns Thông tin về job
   */
  async getJobStatus(queueName: string, jobId: string | undefined): Promise<any> {
    try {
      let queue: Queue;

      switch (queueName) {
        case QueueName.EMAIL:
          queue = this.emailQueue;
          break;
        case QueueName.SMS:
          queue = this.smsQueue;
          break;
        case QueueName.NOTIFICATION:
          queue = this.notificationQueue;
          break;
        case QueueName.DATA_PROCESS:
          queue = this.dataProcessQueue;
          break;
        case QueueName.SEND_SYSTEM_EMAIL:
          queue = this.systemEmailQueue;
          break;
        case QueueName.AGENT:
          queue = this.agentQueue;
          break;
        case QueueName.EMAIL_SYSTEM:
          queue = this.emailSystemQueue;
          break;
        case QueueName.CRAWL_URL:
          queue = this.crawlUrlQueue;
          break;
        case QueueName.CRAWL_URL_ADMIN:
          queue = this.crawlUrlAdminQueue;
          break;
        default:
          throw new AppException(ErrorCode.VALIDATION_ERROR, `Queue không tồn tại: ${queueName}`);
      }

      if (!jobId) {
        throw new AppException(ErrorCode.VALIDATION_ERROR, 'ID job không hợp lệ');
      }

      const job = await queue.getJob(jobId);

      if (!job) {
        throw new AppException(ErrorCode.RESOURCE_NOT_FOUND, `Không tìm thấy job với ID: ${jobId}`);
      }

      const state = await job.getState();

      return {
        id: job.id,
        data: job.data,
        state,
        progress: job.progress,
        attemptsMade: job.attemptsMade,
        failedReason: job.failedReason,
        stacktrace: job.stacktrace,
        timestamp: job.timestamp,
      };
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Lỗi khi kiểm tra trạng thái job: ${error.message}`, error.stack);
      throw new AppException(ErrorCode.INTERNAL_SERVER_ERROR, 'Không thể kiểm tra trạng thái job');
    }
  }

  /**
   * ✅ Monitor job sau khi add để debug vấn đề worker không xử lý ngay lập tức
   */
  private monitorJobAfterAdd(jobId: string, type: 'USER' | 'ADMIN'): void {
    const queue = type === 'USER' ? this.crawlUrlQueue : this.crawlUrlAdminQueue;
    const prefix = type === 'USER' ? 'User' : 'Admin';

    // Check ngay lập tức
    setTimeout(async () => {
      try {
        this.logger.log(`🔍 ${prefix} job monitoring: Checking job ${jobId} after 1s...`);

        const waiting = await queue.getWaiting();
        const active = await queue.getActive();
        const completed = await queue.getCompleted();
        const failed = await queue.getFailed();

        this.logger.log(`🔍 ${prefix} queue status: waiting=${waiting.length}, active=${active.length}, completed=${completed.length}, failed=${failed.length}`);

        // Kiểm tra job cụ thể
        const specificJob = await queue.getJob(jobId);
        if (specificJob) {
          const state = await specificJob.getState();
          this.logger.log(`🔍 ${prefix} job ${jobId} state: ${state}`);

          if (state === 'waiting') {
            this.logger.warn(`⚠️ ${prefix} job ${jobId} vẫn đang chờ sau 1s - có thể worker chưa sẵn sàng`);
          }
        } else {
          this.logger.error(`❌ ${prefix} job ${jobId} không tìm thấy trong queue!`);
        }

      } catch (err) {
        this.logger.error(`❌ Error monitoring ${prefix.toLowerCase()} job: ${err.message}`);
      }
    }, 1000);

    // Check sau 5 giây
    setTimeout(async () => {
      try {
        this.logger.log(`🔍 ${prefix} job monitoring: Checking job ${jobId} after 5s...`);

        const specificJob = await queue.getJob(jobId);
        if (specificJob) {
          const state = await specificJob.getState();
          this.logger.log(`🔍 ${prefix} job ${jobId} state after 5s: ${state}`);

          if (state === 'waiting') {
            this.logger.error(`🚨 ${prefix} job ${jobId} VẪN ĐANG CHỜ sau 5s - Worker có vấn đề!`);

            // Log thêm thông tin debug
            const waiting = await queue.getWaiting();
            const active = await queue.getActive();
            this.logger.error(`🚨 ${prefix} queue debug: waiting=${waiting.length}, active=${active.length}`);

            // Kiểm tra xem có job nào đang active không
            if (active.length === 0) {
              this.logger.error(`🚨 ${prefix} worker không xử lý job nào - Worker có thể không hoạt động!`);
            }
          } else if (state === 'active') {
            this.logger.log(`✅ ${prefix} job ${jobId} đã được worker nhận và đang xử lý`);
          } else if (state === 'completed') {
            this.logger.log(`✅ ${prefix} job ${jobId} đã hoàn thành nhanh chóng`);
          } else if (state === 'failed') {
            this.logger.error(`❌ ${prefix} job ${jobId} đã thất bại: ${specificJob.failedReason}`);
          }
        } else {
          this.logger.error(`❌ ${prefix} job ${jobId} đã biến mất khỏi queue`);
        }

      } catch (err) {
        this.logger.error(`❌ Error monitoring ${prefix.toLowerCase()} job after 5s: ${err.message}`);
      }
    }, 5000);
  }

  /**
   * ✅ Đảm bảo queue connection sẵn sàng trước khi thêm job
   */
  private async ensureQueueConnectionReady(queue: Queue, queueName: string): Promise<void> {
    try {
      this.logger.log(`🔄 Kiểm tra Redis connection cho queue ${queueName}...`);

      // Thử thực hiện một operation đơn giản để test connection
      const waitingJobs = await queue.getWaiting();

      this.logger.log(`✅ Redis connection sẵn sàng cho queue ${queueName} (waiting jobs: ${waitingJobs.length})`);
    } catch (error) {
      this.logger.error(`❌ Redis connection không sẵn sàng cho queue ${queueName}: ${error.message}`);

      // Retry một lần
      try {
        this.logger.log(`🔄 Retry kiểm tra connection cho queue ${queueName}...`);
        await new Promise(resolve => setTimeout(resolve, 1000));
        await queue.getWaiting();
        this.logger.log(`✅ Redis connection sẵn sàng cho queue ${queueName} sau retry`);
      } catch (retryError) {
        this.logger.error(`❌ Retry thất bại cho queue ${queueName}: ${retryError.message}`);
        throw new AppException(ErrorCode.REDIS_ERROR, `Redis connection không sẵn sàng cho queue ${queueName}`, retryError);
      }
    }
  }
}