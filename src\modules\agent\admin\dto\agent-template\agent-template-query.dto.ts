import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsEnum, IsOptional } from 'class-validator';
import { QueryDto, SortDirection } from '@common/dto/query.dto';

/**
 * Enum cho các trường sắp xếp của agent template
 */
export enum AgentTemplateSortBy {
  CREATED_AT = 'createdAt',
  UPDATED_AT = 'updatedAt',
  NAME = 'name',
  TYPE_ID = 'typeId'
}

/**
 * DTO cho việc truy vấn danh sách agent template
 */
export class AgentTemplateQueryDto extends QueryDto {

  /**
   * Sắp xếp theo trường
   */
  @ApiPropertyOptional({
    description: 'Sắp xếp theo trường',
    enum: AgentTemplateSortBy,
    example: AgentTemplateSortBy.CREATED_AT,
  })
  @IsOptional()
  @IsEnum(AgentTemplateSortBy)
  sortBy?: AgentTemplateSortBy = AgentTemplateSortBy.CREATED_AT;

  /**
   * Hướng sắp xếp
   */
  @ApiPropertyOptional({
    description: 'Hướng sắp xếp',
    enum: SortDirection,
    example: SortDirection.DESC,
  })
  @IsOptional()
  @IsEnum(SortDirection)
  sortDirection?: SortDirection = SortDirection.DESC;
}
