import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class AddSignatureFieldsToRuleContract1734534000000 implements MigrationInterface {
  name = 'AddSignatureFieldsToRuleContract1734534000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Thêm cột user_signature_data để lưu dữ liệu chữ ký
    await queryRunner.addColumn(
      'rule_contract',
      new TableColumn({
        name: 'user_signature_data',
        type: 'text',
        isNullable: true,
        comment: 'Dữ liệu chữ ký người dùng (base64 cho cá nhân, S3 key cho doanh nghiệp)',
      }),
    );

    // Thêm cột signature_type để phân biệt loại chữ ký
    await queryRunner.addColumn(
      'rule_contract',
      new TableColumn({
        name: 'signature_type',
        type: 'varchar',
        length: '20',
        isNullable: true,
        comment: '<PERSON><PERSON><PERSON> k<PERSON> (base64 hoặc s3_key)',
      }),
    );

    // Thêm cột signer_name đ<PERSON> lưu tên người ký (cho doanh nghiệp)
    await queryRunner.addColumn(
      'rule_contract',
      new TableColumn({
        name: 'signer_name',
        type: 'varchar',
        length: '100',
        isNullable: true,
        comment: 'Tên người ký (cho doanh nghiệp)',
      }),
    );

    // Thêm cột signer_position để lưu chức vụ người ký (cho doanh nghiệp)
    await queryRunner.addColumn(
      'rule_contract',
      new TableColumn({
        name: 'signer_position',
        type: 'varchar',
        length: '255',
        isNullable: true,
        comment: 'Chức vụ người ký (cho doanh nghiệp)',
      }),
    );

    console.log('✅ Added signature fields to rule_contract table');
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Xóa các cột đã thêm (theo thứ tự ngược lại)
    await queryRunner.dropColumn('rule_contract', 'signer_position');
    await queryRunner.dropColumn('rule_contract', 'signer_name');
    await queryRunner.dropColumn('rule_contract', 'signature_type');
    await queryRunner.dropColumn('rule_contract', 'user_signature_data');

    console.log('✅ Removed signature fields from rule_contract table');
  }
}
