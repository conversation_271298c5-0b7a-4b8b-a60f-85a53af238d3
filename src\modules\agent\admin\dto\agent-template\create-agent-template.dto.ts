import { AgentTemplateStatus } from '@modules/agent/constants';
import { ProfileAgent } from '@modules/agent/interfaces/profile-agent.interface';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsArray,
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsObject,
  IsOptional,
  IsString,
  ValidateNested
} from 'class-validator';
import { ModelConfigDto } from '../agent-system';
import { ConversionConfigDto } from './conversion-config.dto';
import { AgentMemoryDto } from './agent-memory.dto';

/**
 * DTO cho việc tạo agent template mới
 */
export class CreateAgentTemplateDto {
  /**
   * Tên hiển thị của agent
   */
  @ApiProperty({
    description: 'Tên hiển thị của agent',
    example: 'System Assistant',
  })
  @IsString()
  @IsNotEmpty()
  name: string;

  /**
   * MIME type của avatar
   */
  @ApiPropertyOptional({
    description: 'MIME type của avatar',
    example: 'image/jpeg',
  })
  @IsString()
  @IsOptional()
  avatarMimeType?: string;

  /**
   * Cấu hình model AI
   */
  @ApiProperty({
    description: 'Cấu hình model AI',
    type: ModelConfigDto,
  })
  @ValidateNested()
  @Type(() => ModelConfigDto)
  @IsObject()
  modelConfig: ModelConfigDto;

  /**
   * Thông tin hồ sơ mẫu
   */
  @ApiPropertyOptional({
    description: 'Thông tin hồ sơ mẫu',
    type: Object,
    example: {
      gender: 'MALE',
      dateOfBirth: '1990-01-01',
      position: 'Developer',
      education: 'Bachelor',
      skills: ['JavaScript', 'Python'],
      personality: ['Creative', 'Team-player'],
      languages: ['English', 'Vietnamese'],
      nations: 'Vietnam'
    }
  })
  @IsObject()
  @IsOptional()
  @Type(() => Object)
  profile?: ProfileAgent;

  /**
   * Hướng dẫn hoặc system prompt cho agent
   */
  @ApiPropertyOptional({
    description: 'Hướng dẫn hoặc system prompt cho agent',
    example: 'Bạn là trợ lý hệ thống, hãy giúp người dùng giải đáp các thắc mắc',
  })
  @IsString()
  @IsOptional()
  instruction?: string;

  /**
   * ID của vector store
   */
  @ApiPropertyOptional({
    description: 'ID của vector store',
    example: 'vector-store-1',
  })
  @IsString()
  @IsOptional()
  vectorStoreId?: string;

  /**
   * Trạng thái của agent template
   */
  @ApiProperty({
    description: 'Trạng thái của agent template',
    enum: AgentTemplateStatus,
    example: AgentTemplateStatus.DRAFT,
  })
  @IsEnum(AgentTemplateStatus)
  status: AgentTemplateStatus;

  /**
   * Cấu hình conversion mới
   */
  @ApiPropertyOptional({
    description: 'Cấu hình conversion mới',
    type: [ConversionConfigDto],
    example: [
      {
        name: 'email',
        type: 'string',
        description: 'Địa chỉ email người dùng',
        required: true,
        active: true
      }
    ]
  })
  @IsArray()
  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => ConversionConfigDto)
  conversion?: ConversionConfigDto[];

  /**
   * ID của loại agent
   */
  @ApiProperty({
    description: 'ID của loại agent',
    example: 1,
  })
  @IsNumber()
  typeId: number;

  /**
   * ID của system model
   */
  @ApiPropertyOptional({
    description: 'ID của system model',
    example: 'model-system-uuid',
  })
  @IsString()
  @IsOptional()
  modelSystemId?: string;

  /**
   * ID của strategy
   */
  @ApiPropertyOptional({
    description: 'ID của strategy',
    example: 'strategy-uuid',
  })
  @IsString()
  @IsOptional()
  strategyId?: string;

  /**
   * Memories của agent
   */
  @ApiPropertyOptional({
    description: 'Memories của agent',
    type: [AgentMemoryDto],
    example: [
      {
        title: 'memories',
        reason: 'memories',
        content: 'memories'
      }
    ]
  })
  @IsArray()
  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => AgentMemoryDto)
  memories?: AgentMemoryDto[];
}
