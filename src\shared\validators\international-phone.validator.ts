import { registerDecorator, ValidationOptions, ValidationArguments, ValidatorConstraint, ValidatorConstraintInterface } from 'class-validator';
import { parsePhoneNumber, isValidPhoneNumber, CountryCode } from 'libphonenumber-js';

@ValidatorConstraint({ name: 'isInternationalPhone', async: false })
export class IsInternationalPhoneConstraint implements ValidatorConstraintInterface {
  validate(phone: string, args: ValidationArguments) {
    if (!phone) {
      return true; // Let @IsOptional handle empty values
    }

    try {
      // Validate using libphonenumber-js with international format
      return isValidPhoneNumber(phone);
    } catch (error) {
      return false;
    }
  }

  defaultMessage(args: ValidationArguments) {
    return 'Số điện thoại không hợp lệ, phải là số điện thoại quốc tế hợp lệ (ví dụ: +84912345678)';
  }
}

/**
 * Decorator để validate số điện tho<PERSON>i quốc tế chuẩn
 * Sử dụng thư viện libphonenumber-js để validate số điện thoại theo chuẩn quốc tế
 * 
 * @param validationOptions Tùy chọn validation
 * @returns Decorator function
 * 
 * @example
 * ```typescript
 * class CreateUserDto {
 *   @IsInternationalPhone()
 *   @IsNotEmpty()
 *   phoneNumber: string;
 * }
 * ```
 */
export function IsInternationalPhone(validationOptions?: ValidationOptions) {
  return function (object: Object, propertyName: string) {
    registerDecorator({
      target: object.constructor,
      propertyName: propertyName,
      options: validationOptions,
      constraints: [],
      validator: IsInternationalPhoneConstraint,
    });
  };
}

@ValidatorConstraint({ name: 'isInternationalPhoneWithCountry', async: false })
export class IsInternationalPhoneWithCountryConstraint implements ValidatorConstraintInterface {
  validate(phone: string, args: ValidationArguments) {
    if (!phone) {
      return true; // Let @IsOptional handle empty values
    }

    const object = args.object as any;
    const countryCode = object.countryCode;

    if (!countryCode) {
      // Nếu không có country code, validate như số điện thoại quốc tế thông thường
      try {
        return isValidPhoneNumber(phone);
      } catch (error) {
        return false;
      }
    }

    try {
      // Remove + from country code to get the numeric part
      const numericCountryCode = countryCode.replace('+', '');
      
      // Combine country code with phone number
      const fullPhoneNumber = `+${numericCountryCode}${phone.replace(/^0+/, '')}`;
      
      // Validate using libphonenumber-js
      return isValidPhoneNumber(fullPhoneNumber);
    } catch (error) {
      return false;
    }
  }

  defaultMessage(args: ValidationArguments) {
    return 'Số điện thoại không hợp lệ với mã quốc gia được cung cấp';
  }
}

/**
 * Decorator để validate số điện thoại với mã quốc gia
 * Sử dụng khi có field countryCode trong cùng DTO
 * 
 * @param validationOptions Tùy chọn validation
 * @returns Decorator function
 * 
 * @example
 * ```typescript
 * class CreateUserDto {
 *   @IsString()
 *   countryCode: string; // "+84"
 * 
 *   @IsInternationalPhoneWithCountry()
 *   @IsNotEmpty()
 *   phoneNumber: string; // "912345678"
 * }
 * ```
 */
export function IsInternationalPhoneWithCountry(validationOptions?: ValidationOptions) {
  return function (object: Object, propertyName: string) {
    registerDecorator({
      target: object.constructor,
      propertyName: propertyName,
      options: validationOptions,
      constraints: [],
      validator: IsInternationalPhoneWithCountryConstraint,
    });
  };
}

/**
 * Utility function để format số điện thoại về định dạng quốc tế
 * @param phone Số điện thoại cần format
 * @param defaultCountry Mã quốc gia mặc định (optional)
 * @returns Số điện thoại đã format hoặc null nếu không hợp lệ
 */
export function formatInternationalPhone(phone: string, defaultCountry?: CountryCode): string | null {
  if (!phone) return null;

  try {
    const phoneNumber = parsePhoneNumber(phone, defaultCountry);
    if (phoneNumber && phoneNumber.isValid()) {
      return phoneNumber.formatInternational();
    }
    return null;
  } catch (error) {
    return null;
  }
}

/**
 * Utility function để lấy thông tin chi tiết về số điện thoại
 * @param phone Số điện thoại cần phân tích
 * @param defaultCountry Mã quốc gia mặc định (optional)
 * @returns Thông tin chi tiết về số điện thoại
 */
export function getPhoneInfo(phone: string, defaultCountry?: CountryCode) {
  if (!phone) return null;

  try {
    const phoneNumber = parsePhoneNumber(phone, defaultCountry);
    if (phoneNumber && phoneNumber.isValid()) {
      return {
        isValid: true,
        international: phoneNumber.formatInternational(),
        national: phoneNumber.formatNational(),
        e164: phoneNumber.format('E.164'),
        country: phoneNumber.country,
        countryCallingCode: phoneNumber.countryCallingCode,
        type: phoneNumber.getType(),
      };
    }
    return {
      isValid: false,
      international: null,
      national: null,
      e164: null,
      country: null,
      countryCallingCode: null,
      type: null,
    };
  } catch (error) {
    return {
      isValid: false,
      international: null,
      national: null,
      e164: null,
      country: null,
      countryCallingCode: null,
      type: null,
      error: error.message,
    };
  }
}
