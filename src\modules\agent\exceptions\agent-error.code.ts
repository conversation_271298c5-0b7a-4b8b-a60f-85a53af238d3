import { HttpStatus } from '@nestjs/common';
import { ErrorCode } from '@common/exceptions';

/**
 * Mã lỗi liên quan đến module Agent (40000-40099)
 */
export const AGENT_ERROR_CODES = {
  // ===== TYPE AGENT ERRORS (40000-40009) =====
  /**
   * Lỗi khi không tìm thấy loại agent
   */
  TYPE_AGENT_NOT_FOUND: new ErrorCode(
    40000,
    'Không tìm thấy loại agent',
    HttpStatus.NOT_FOUND,
  ),

  /**
   * Lỗi khi tên loại agent đã tồn tại
   */
  TYPE_AGENT_NAME_EXISTS: new ErrorCode(
    40001,
    'Tên loại agent đã tồn tại',
    HttpStatus.CONFLICT,
  ),

  /**
   * Lỗi khi cập nhật trạng thái loại agent thất bại
   */
  TYPE_AGENT_STATUS_UPDATE_FAILED: new ErrorCode(
    40002,
    'Cập nhật trạng thái loại agent thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  /**
   * Lỗi khi không tìm thấy group tool
   */
  GROUP_TOOL_NOT_FOUND: new ErrorCode(
    40003,
    'Không tìm thấy group tool',
    HttpStatus.NOT_FOUND,
  ),

  /**
   * Lỗi khi loại agent đã bị xóa mềm
   */
  TYPE_AGENT_ALREADY_DELETED: new ErrorCode(
    40004,
    'Loại agent đã bị xóa',
    HttpStatus.BAD_REQUEST,
  ),

  // ===== AGENT SYSTEM ERRORS (40010-40019) =====
  /**
   * Lỗi khi không tìm thấy agent system
   */
  AGENT_SYSTEM_NOT_FOUND: new ErrorCode(
    40010,
    'Không tìm thấy agent system',
    HttpStatus.NOT_FOUND,
  ),

  /**
   * Lỗi khi tên agent system đã tồn tại
   */
  AGENT_SYSTEM_NAME_EXISTS: new ErrorCode(
    40011,
    'Tên agent system đã tồn tại',
    HttpStatus.CONFLICT,
  ),

  /**
   * Lỗi khi cập nhật trạng thái agent system thất bại
   */
  AGENT_SYSTEM_STATUS_UPDATE_FAILED: new ErrorCode(
    40012,
    'Cập nhật trạng thái agent system thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  /**
   * Lỗi khi không tìm thấy model
   */
  MODEL_NOT_FOUND: new ErrorCode(
    40013,
    'Không tìm thấy model',
    HttpStatus.NOT_FOUND,
  ),

  /**
   * Lỗi khi cấu hình model không hợp lệ
   */
  INVALID_MODEL_CONFIG: new ErrorCode(
    40014,
    'Cấu hình model không hợp lệ',
    HttpStatus.BAD_REQUEST,
  ),

  /**
   * Lỗi khi nhà cung cấp model không khớp với model
   */
  MODEL_PROVIDER_MISMATCH: new ErrorCode(
    40017,
    'Nhà cung cấp model không khớp với model',
    HttpStatus.BAD_REQUEST,
  ),

  /**
   * Lỗi khi không tìm thấy vector store
   */
  VECTOR_STORE_NOT_FOUND: new ErrorCode(
    40015,
    'Không tìm thấy vector store',
    HttpStatus.NOT_FOUND,
  ),

  /**
   * Lỗi khi mã định danh agent system đã tồn tại
   */
  AGENT_SYSTEM_NAME_CODE_EXISTS: new ErrorCode(
    40016,
    'Mã định danh agent system đã tồn tại',
    HttpStatus.CONFLICT,
  ),

  /**
   * Lỗi khi không thể gán agent supervisor cho type agent
   */
  AGENT_SYSTEM_SUPERVISOR_NOT_ALLOWED: new ErrorCode(
    40017,
    'Không thể gán agent supervisor cho type agent. Chỉ được chọn agent bình thường.',
    HttpStatus.BAD_REQUEST,
  ),

  // ===== AGENT BASE ERRORS (40020-40029) =====
  /**
   * Lỗi khi không tìm thấy agent base
   */
  AGENT_BASE_NOT_FOUND: new ErrorCode(
    40020,
    'Không tìm thấy agent base',
    HttpStatus.NOT_FOUND,
  ),

  /**
   * Lỗi khi agent base đã tồn tại
   */
  AGENT_BASE_ALREADY_EXISTS: new ErrorCode(
    40021,
    'Agent base đã tồn tại',
    HttpStatus.CONFLICT,
  ),

  /**
   * Lỗi khi tạo agent base thất bại
   */
  AGENT_BASE_CREATION_FAILED: new ErrorCode(
    40022,
    'Tạo agent base thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  /**
   * Lỗi khi cập nhật agent base thất bại
   */
  AGENT_BASE_UPDATE_FAILED: new ErrorCode(
    40023,
    'Cập nhật agent base thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  /**
   * Lỗi khi xóa agent base thất bại
   */
  AGENT_BASE_DELETE_FAILED: new ErrorCode(
    40024,
    'Xóa agent base thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  /**
   * Lỗi khi truy vấn agent thất bại
   */
  AGENT_QUERY_FAILED: new ErrorCode(
    40016,
    'Truy vấn agent thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  /**
   * Lỗi khi không tìm thấy agent
   */
  AGENT_NOT_FOUND: new ErrorCode(
    40017,
    'Không tìm thấy agent',
    HttpStatus.NOT_FOUND,
  ),

  /**
   * Lỗi khi cập nhật trạng thái active của agent base thất bại
   */
  AGENT_BASE_ACTIVE_UPDATE_FAILED: new ErrorCode(
    40025,
    'Cập nhật trạng thái active của agent base thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  // ===== AGENT TEMPLATE ERRORS (40030-40039) =====
  /**
   * Lỗi khi không tìm thấy agent template
   */
  AGENT_TEMPLATE_NOT_FOUND: new ErrorCode(
    40030,
    'Không tìm thấy agent template',
    HttpStatus.NOT_FOUND,
  ),

  /**
   * Lỗi khi tên agent template đã tồn tại
   */
  AGENT_TEMPLATE_NAME_EXISTS: new ErrorCode(
    40031,
    'Tên agent template đã tồn tại',
    HttpStatus.CONFLICT,
  ),

  /**
   * Lỗi khi cập nhật trạng thái agent template thất bại
   */
  AGENT_TEMPLATE_STATUS_UPDATE_FAILED: new ErrorCode(
    40032,
    'Cập nhật trạng thái agent template thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  /**
   * Lỗi khi tạo agent template thất bại
   */
  AGENT_TEMPLATE_CREATE_FAILED: new ErrorCode(
    40033,
    'Tạo agent template thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  /**
   * Lỗi khi cập nhật agent template thất bại
   */
  AGENT_TEMPLATE_UPDATE_FAILED: new ErrorCode(
    40034,
    'Cập nhật agent template thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  /**
   * Lỗi khi xóa agent template thất bại
   */
  AGENT_TEMPLATE_DELETE_FAILED: new ErrorCode(
    40035,
    'Xóa agent template thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  /**
   * Lỗi khi lấy thông tin agent template thất bại
   */
  AGENT_TEMPLATE_FETCH_FAILED: new ErrorCode(
    40036,
    'Lỗi khi lấy thông tin agent template',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  /**
   * Lỗi khi khôi phục agent template thất bại
   */
  AGENT_TEMPLATE_RESTORE_FAILED: new ErrorCode(
    40037,
    'Khôi phục agent template thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  /**
   * Lỗi khi agent template đã bị xóa mềm
   */
  AGENT_TEMPLATE_ALREADY_DELETED: new ErrorCode(
    40038,
    'Agent template đã bị xóa',
    HttpStatus.BAD_REQUEST,
  ),

  // ===== AGENT ROLE & PERMISSION ERRORS (40040-40049) =====
  /**
   * Lỗi khi tạo quyền thất bại
   */
  AGENT_PERMISSION_CREATE_FAILED: new ErrorCode(
    40046,
    'Tạo quyền thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  /**
   * Lỗi khi cập nhật quyền thất bại
   */
  AGENT_PERMISSION_UPDATE_FAILED: new ErrorCode(
    40047,
    'Cập nhật quyền thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  /**
   * Lỗi khi xóa quyền thất bại
   */
  AGENT_PERMISSION_DELETE_FAILED: new ErrorCode(
    40048,
    'Xóa quyền thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  /**
   * Lỗi khi quyền đang được sử dụng
   */
  AGENT_PERMISSION_IN_USE: new ErrorCode(
    40049,
    'Quyền đang được sử dụng',
    HttpStatus.CONFLICT,
  ),

  /**
   * Lỗi khi gán quyền cho vai trò thất bại
   */
  AGENT_PERMISSION_ASSIGN_FAILED: new ErrorCode(
    40050,
    'Gán quyền cho vai trò thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
  /**
   * Lỗi khi không tìm thấy vai trò
   */
  AGENT_ROLE_NOT_FOUND: new ErrorCode(
    40040,
    'Không tìm thấy vai trò',
    HttpStatus.NOT_FOUND,
  ),

  /**
   * Lỗi khi tên vai trò đã tồn tại
   */
  AGENT_ROLE_NAME_EXISTS: new ErrorCode(
    40041,
    'Tên vai trò đã tồn tại',
    HttpStatus.CONFLICT,
  ),

  /**
   * Lỗi khi không tìm thấy quyền
   */
  AGENT_PERMISSION_NOT_FOUND: new ErrorCode(
    40042,
    'Không tìm thấy quyền',
    HttpStatus.NOT_FOUND,
  ),

  /**
   * Lỗi khi tên quyền đã tồn tại
   */
  AGENT_PERMISSION_NAME_EXISTS: new ErrorCode(
    40043,
    'Tên quyền đã tồn tại',
    HttpStatus.CONFLICT,
  ),

  /**
   * Lỗi khi quyền đã được gán cho vai trò khác
   */
  AGENT_PERMISSION_ALREADY_ASSIGNED: new ErrorCode(
    40044,
    'Quyền đã được gán cho vai trò khác',
    HttpStatus.CONFLICT,
  ),

  /**
   * Lỗi khi vai trò đã được gán cho agent khác
   */
  AGENT_ROLE_ALREADY_ASSIGNED: new ErrorCode(
    40045,
    'Vai trò đã được gán cho agent khác',
    HttpStatus.CONFLICT,
  ),

  // ===== AGENT USER ERRORS (40050-40059) =====
  /**
   * Lỗi khi không tìm thấy agent user
   */
  AGENT_USER_NOT_FOUND: new ErrorCode(
    40050,
    'Không tìm thấy agent user',
    HttpStatus.NOT_FOUND,
  ),

  /**
   * Lỗi khi tên agent user đã tồn tại
   */
  AGENT_USER_NAME_EXISTS: new ErrorCode(
    40051,
    'Tên agent user đã tồn tại',
    HttpStatus.CONFLICT,
  ),

  /**
   * Lỗi khi cập nhật trạng thái agent user thất bại
   */
  AGENT_USER_STATUS_UPDATE_FAILED: new ErrorCode(
    40052,
    'Cập nhật trạng thái agent user thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  // ===== AGENT RESOURCE ERRORS (40060-40069) =====
  /**
   * Lỗi khi không tìm thấy media
   */
  MEDIA_NOT_FOUND: new ErrorCode(
    40060,
    'Không tìm thấy media',
    HttpStatus.NOT_FOUND,
  ),

  /**
   * Lỗi khi không tìm thấy URL
   */
  URL_NOT_FOUND: new ErrorCode(
    40061,
    'Không tìm thấy URL',
    HttpStatus.NOT_FOUND,
  ),

  /**
   * Lỗi khi không tìm thấy sản phẩm
   */
  PRODUCT_NOT_FOUND: new ErrorCode(
    40062,
    'Không tìm thấy sản phẩm',
    HttpStatus.NOT_FOUND,
  ),

  /**
   * Lỗi khi không tìm thấy chiến lược
   */
  STRATEGY_NOT_FOUND: new ErrorCode(
    40063,
    'Không tìm thấy chiến lược',
    HttpStatus.NOT_FOUND,
  ),

  /**
   * Lỗi khi tạo chiến lược agent thất bại
   */
  STRATEGY_CREATION_FAILED: new ErrorCode(
    40065,
    'Tạo chiến lược agent thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  /**
   * Lỗi khi cập nhật chiến lược agent thất bại
   */
  STRATEGY_UPDATE_FAILED: new ErrorCode(
    40066,
    'Cập nhật chiến lược agent thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  /**
   * Lỗi khi xóa chiến lược agent thất bại
   */
  STRATEGY_DELETE_FAILED: new ErrorCode(
    40067,
    'Xóa chiến lược agent thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  /**
   * Lỗi khi khôi phục chiến lược agent thất bại
   */
  STRATEGY_RESTORE_FAILED: new ErrorCode(
    40068,
    'Khôi phục chiến lược agent thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  /**
   * Lỗi khi chiến lược agent đang được sử dụng
   */
  STRATEGY_IN_USE: new ErrorCode(
    40069,
    'Không thể xóa chiến lược agent đang được sử dụng',
    HttpStatus.BAD_REQUEST,
  ),

  /**
   * Lỗi khi không tìm thấy công cụ
   */
  TOOL_NOT_FOUND: new ErrorCode(
    40064,
    'Không tìm thấy công cụ',
    HttpStatus.NOT_FOUND,
  ),

  // ===== GENERAL AGENT ERRORS (40070-40099) =====
  /**
   * Lỗi khi tạo S3 key không hợp lệ
   */
  INVALID_S3_KEY: new ErrorCode(
    40070,
    'S3 key không hợp lệ',
    HttpStatus.BAD_REQUEST,
  ),

  // Đã được định nghĩa ở dòng 158

  /**
   * Lỗi khi tạo agent thất bại
   */
  AGENT_CREATION_FAILED: new ErrorCode(
    40072,
    'Lỗi khi tạo agent',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  /**
   * Lỗi khi cập nhật agent thất bại
   */
  AGENT_UPDATE_FAILED: new ErrorCode(
    40073,
    'Lỗi khi cập nhật agent',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  /**
   * Lỗi khi xóa agent thất bại
   */
  AGENT_DELETE_FAILED: new ErrorCode(
    40074,
    'Lỗi khi xóa agent',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  /**
   * Lỗi khi gửi tin nhắn đến agent thất bại
   */
  AGENT_CHAT_FAILED: new ErrorCode(
    40075,
    'Lỗi khi gửi tin nhắn đến agent',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  /**
   * Lỗi khi lấy danh sách agent thất bại
   */
  AGENT_LIST_FAILED: new ErrorCode(
    40076,
    'Lỗi khi lấy danh sách agent',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  AGENT_STATISTICS_FAILED: new ErrorCode(
    40077,
    'Lỗi khi lấy thống kê agent',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  /**
   * Lỗi khi lấy chi tiết agent thất bại
   */
  AGENT_DETAIL_FAILED: new ErrorCode(
    40077,
    'Lỗi khi lấy chi tiết agent',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  /**
   * Lỗi khi người dùng không có quyền truy cập agent
   */
  AGENT_ACCESS_DENIED: new ErrorCode(
    40078,
    'Không có quyền truy cập agent',
    HttpStatus.FORBIDDEN,
  ),

  /**
   * Lỗi khi tạo loại agent thất bại
   */
  TYPE_AGENT_CREATION_FAILED: new ErrorCode(
    40079,
    'Lỗi khi tạo loại agent',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  /**
   * Lỗi khi không tìm thấy website hoặc không có quyền truy cập
   */
  WEBSITE_NOT_FOUND: new ErrorCode(
    40090,
    'Website không tồn tại hoặc không thuộc về người dùng',
    HttpStatus.NOT_FOUND,
  ),

  // ===== MODULAR AGENT VALIDATION ERRORS (40100-40199) =====
  /**
   * Lỗi khi profile là bắt buộc nhưng không được cung cấp
   */
  AGENT_PROFILE_REQUIRED: new ErrorCode(
    40100,
    'Profile là bắt buộc cho loại agent này',
    HttpStatus.BAD_REQUEST,
  ),

  /**
   * Lỗi khi loại agent không hỗ trợ profile
   */
  AGENT_PROFILE_NOT_SUPPORTED: new ErrorCode(
    40101,
    'Loại agent này không hỗ trợ profile',
    HttpStatus.BAD_REQUEST,
  ),

    /**
   * Lỗi khi loại agent không hỗ trợ profile
   */
  AGENT_CONVERT_NOT_SUPPORTED: new ErrorCode(
    40101,
    'Loại agent này không hỗ trợ convert',
    HttpStatus.BAD_REQUEST,
  ),

  /**
   * Lỗi khi profile không đầy đủ thông tin bắt buộc
   */
  AGENT_PROFILE_INCOMPLETE: new ErrorCode(
    40102,
    'Profile không đầy đủ thông tin bắt buộc',
    HttpStatus.BAD_REQUEST,
  ),

  /**
   * Lỗi khi output configuration là bắt buộc nhưng không được cung cấp
   */
  AGENT_OUTPUT_REQUIRED: new ErrorCode(
    40103,
    'Output configuration là bắt buộc cho loại agent này',
    HttpStatus.BAD_REQUEST,
  ),

  /**
   * Lỗi khi loại agent không hỗ trợ output configuration
   */
  AGENT_OUTPUT_NOT_SUPPORTED: new ErrorCode(
    40104,
    'Loại agent này không hỗ trợ output configuration',
    HttpStatus.BAD_REQUEST,
  ),

  /**
   * Lỗi khi output configuration không đầy đủ
   */
  AGENT_OUTPUT_INCOMPLETE: new ErrorCode(
    40105,
    'Output configuration không đầy đủ',
    HttpStatus.BAD_REQUEST,
  ),

  /**
   * Lỗi khi loại agent không hỗ trợ resources
   */
  AGENT_RESOURCES_NOT_SUPPORTED: new ErrorCode(
    40106,
    'Loại agent này không hỗ trợ resources',
    HttpStatus.BAD_REQUEST,
  ),

  /**
   * Lỗi khi resources configuration không đầy đủ
   */
  AGENT_RESOURCES_INCOMPLETE: new ErrorCode(
    40107,
    'Resources configuration không đầy đủ',
    HttpStatus.BAD_REQUEST,
  ),

  /**
   * Lỗi khi loại agent không hỗ trợ strategy
   */
  AGENT_STRATEGY_NOT_SUPPORTED: new ErrorCode(
    40108,
    'Loại agent này không hỗ trợ strategy',
    HttpStatus.BAD_REQUEST,
  ),

  /**
   * Lỗi khi strategy configuration không đầy đủ
   */
  AGENT_STRATEGY_INCOMPLETE: new ErrorCode(
    40109,
    'Strategy configuration không đầy đủ',
    HttpStatus.BAD_REQUEST,
  ),

  /**
   * Lỗi khi loại agent không hỗ trợ multi agent
   */
  AGENT_MULTI_AGENT_NOT_SUPPORTED: new ErrorCode(
    40110,
    'Loại agent này không hỗ trợ multi agent',
    HttpStatus.BAD_REQUEST,
  ),

  /**
   * Lỗi khi multi agent configuration không đầy đủ
   */
  AGENT_MULTI_AGENT_INCOMPLETE: new ErrorCode(
    40111,
    'Multi agent configuration không đầy đủ',
    HttpStatus.BAD_REQUEST,
  ),

  /**
   * Lỗi khi instruction không hợp lệ
   */
  AGENT_INSTRUCTION_INVALID: new ErrorCode(
    40112,
    'Instruction không hợp lệ',
    HttpStatus.BAD_REQUEST,
  ),

  /**
   * Lỗi khi vector store ID không hợp lệ
   */
  AGENT_VECTOR_STORE_INVALID: new ErrorCode(
    40113,
    'Vector store ID không hợp lệ',
    HttpStatus.BAD_REQUEST,
  ),

  /**
   * Lỗi khi cấu hình multi agent không hợp lệ
   */
  INVALID_MULTI_AGENT_CONFIG: new ErrorCode(
    40114,
    'Cấu hình multi agent không hợp lệ',
    HttpStatus.BAD_REQUEST,
  ),

  /**
   * Lỗi khi website đã được tích hợp với agent khác
   */
  WEBSITE_ALREADY_INTEGRATED: new ErrorCode(
    40091,
    'Website đã được tích hợp với agent khác',
    HttpStatus.CONFLICT,
  ),

  /**
   * Lỗi khi website chưa được tích hợp với agent
   */
  WEBSITE_NOT_INTEGRATED: new ErrorCode(
    40092,
    'Website chưa được tích hợp với agent',
    HttpStatus.BAD_REQUEST,
  ),

  /**
   * Lỗi khi tích hợp website với agent thất bại
   */
  WEBSITE_INTEGRATION_FAILED: new ErrorCode(
    40093,
    'Lỗi khi tích hợp website với agent',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  /**
   * Lỗi khi lấy danh sách website thất bại
   */
  WEBSITE_LIST_FAILED: new ErrorCode(
    40094,
    'Lỗi khi lấy danh sách website',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  /**
   * Lỗi khi cập nhật website thất bại
   */
  WEBSITE_UPDATE_FAILED: new ErrorCode(
    40095,
    'Lỗi khi cập nhật website',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  /**
   * Lỗi khi gỡ website khỏi agent thất bại
   */
  WEBSITE_REMOVE_FAILED: new ErrorCode(
    40096,
    'Lỗi khi gỡ website khỏi agent',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  /**
   * Lỗi khi không tìm thấy Facebook page hoặc không có quyền truy cập
   */
  FACEBOOK_PAGE_NOT_FOUND: new ErrorCode(
    40091,
    'Facebook page không tồn tại hoặc không thuộc về người dùng',
    HttpStatus.NOT_FOUND,
  ),

  /**
   * Lỗi khi loại đầu ra không hợp lệ
   */
  INVALID_OUTPUT_TYPE: new ErrorCode(
    40092,
    'Loại đầu ra không hợp lệ',
    HttpStatus.BAD_REQUEST,
  ),

  /**
   * Lỗi khi tích hợp Facebook page với agent thất bại
   */
  FACEBOOK_PAGE_INTEGRATION_FAILED: new ErrorCode(
    40094,
    'Tích hợp Facebook page với agent thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  /**
   * Lỗi khi Facebook page chưa được tích hợp với agent
   */
  FACEBOOK_PAGE_NOT_INTEGRATED: new ErrorCode(
    40096,
    'Facebook page chưa được tích hợp với agent',
    HttpStatus.BAD_REQUEST,
  ),

  /**
   * Lỗi khi cập nhật loại agent thất bại
   */
  TYPE_AGENT_UPDATE_FAILED: new ErrorCode(
    40080,
    'Lỗi khi cập nhật loại agent',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  /**
   * Lỗi khi xóa loại agent thất bại
   */
  TYPE_AGENT_DELETE_FAILED: new ErrorCode(
    40081,
    'Lỗi khi xóa loại agent',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  /**
   * Lỗi khi lấy danh sách loại agent thất bại
   */
  TYPE_AGENT_QUERY_FAILED: new ErrorCode(
    40082,
    'Lỗi khi truy vấn danh sách loại agent',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  /**
   * Lỗi khi lấy thông tin loại agent thất bại
   */
  TYPE_AGENT_FETCH_FAILED: new ErrorCode(
    40083,
    'Lỗi khi lấy thông tin loại agent',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  /**
   * Lỗi khi một hoặc nhiều function ID không hợp lệ
   */
  INVALID_FUNCTION_IDS: new ErrorCode(
    40084,
    'Một hoặc nhiều function ID không hợp lệ',
    HttpStatus.BAD_REQUEST,
  ),

  /**
   * Lỗi khi có function ID trùng nhau trong danh sách
   */
  DUPLICATE_FUNCTION_IDS: new ErrorCode(
    40085,
    'Có function ID trùng nhau trong danh sách',
    HttpStatus.BAD_REQUEST,
  ),

  /**
   * Lỗi khi không tìm thấy chi tiết agent
   */
  AGENT_DETAIL_NOT_FOUND: new ErrorCode(
    40086,
    'Không tìm thấy chi tiết agent',
    HttpStatus.NOT_FOUND,
  ),

  /**
   * Lỗi khi truy vấn danh sách agent thất bại
   */
  AGENT_LIST_QUERY_FAILED: new ErrorCode(
    40087,
    'Lỗi khi truy vấn danh sách agent',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  /**
   * Lỗi khi thiết lập quan hệ cha-con không hợp lệ
   */
  INVALID_PARENT_CHILD_RELATIONSHIP: new ErrorCode(
    40088,
    'Quan hệ cha-con không hợp lệ',
    HttpStatus.BAD_REQUEST,
  ),

  /**
   * Lỗi khi lấy tài nguyên agent thất bại
   */
  AGENT_RESOURCE_FAILED: new ErrorCode(
    40089,
    'Lỗi khi lấy tài nguyên agent',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  /**
   * Lỗi khi lấy thông tin agent thất bại
   */
  AGENT_FETCH_FAILED: new ErrorCode(
    40090,
    'Lỗi khi lấy thông tin agent',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  /**
   * Lỗi khi agent đã tồn tại
   */
  AGENT_ALREADY_EXISTS: new ErrorCode(
    40091,
    'Agent đã tồn tại',
    HttpStatus.CONFLICT,
  ),

  /**
   * Lỗi khi tên agent đã tồn tại
   */
  AGENT_NAME_EXISTS: new ErrorCode(
    40092,
    'Tên agent đã tồn tại',
    HttpStatus.CONFLICT,
  ),

  /**
   * Lỗi khi không tìm thấy agent base đang active
   */
  NO_ACTIVE_AGENT_BASE: new ErrorCode(
    40092,
    'Không tìm thấy agent base đang active',
    HttpStatus.NOT_FOUND,
  ),

  /**
   * Lỗi khi cấu hình model chứa trường không hợp lệ
   */
  INVALID_MODEL_CONFIG_FIELD: new ErrorCode(
    40093,
    'Cấu hình model chứa trường không hợp lệ',
    HttpStatus.BAD_REQUEST,
  ),

  /**
   * Lỗi khi cấu hình conversion không hợp lệ
   */
  INVALID_CONVERSION_CONFIG: new ErrorCode(
    40094,
    'Cấu hình conversion không hợp lệ',
    HttpStatus.BAD_REQUEST,
  ),

  /**
   * Lỗi khi xử lý conversion block thất bại
   */
  CONVERSION_PROCESSING_FAILED: new ErrorCode(
    40095,
    'Xử lý conversion block thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  /**
   * Lỗi khi xử lý resources block thất bại
   */
  RESOURCES_PROCESSING_FAILED: new ErrorCode(
    40096,
    'Xử lý resources block thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  /**
   * Lỗi khi xử lý output block thất bại
   */
  OUTPUT_PROCESSING_FAILED: new ErrorCode(
    40097,
    'Xử lý output block thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  /**
   * Lỗi khi xử lý strategy block thất bại
   */
  STRATEGY_PROCESSING_FAILED: new ErrorCode(
    40098,
    'Xử lý strategy block thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  /**
   * Lỗi khi giá trị frequency_penalty vượt quá giới hạn
   */
  FREQUENCY_PENALTY_EXCEEDED: new ErrorCode(
    40094,
    'Giá trị frequency_penalty vượt quá giới hạn cho phép',
    HttpStatus.BAD_REQUEST,
  ),

  /**
   * Lỗi khi giá trị presence_penalty vượt quá giới hạn
   */
  PRESENCE_PENALTY_EXCEEDED: new ErrorCode(
    40095,
    'Giá trị presence_penalty vượt quá giới hạn cho phép',
    HttpStatus.BAD_REQUEST,
  ),

  /**
   * Lỗi khi model chưa được cấu hình trong hệ thống
   */
  MODEL_NOT_CONFIGURED: new ErrorCode(
    40096,
    'Model chưa được cấu hình trong hệ thống, vui lòng liên hệ quản trị viên',
    HttpStatus.BAD_REQUEST,
  ),

  /**
   * Lỗi khi model chưa được phê duyệt
   */
  MODEL_NOT_APPROVED: new ErrorCode(
    40097,
    'Model chưa được phê duyệt, không thể sử dụng',
    HttpStatus.BAD_REQUEST,
  ),

  /**
   * Lỗi khi base model không tồn tại
   */
  BASE_MODEL_NOT_FOUND: new ErrorCode(
    40098,
    'Base model không tồn tại',
    HttpStatus.NOT_FOUND,
  ),

  /**
   * Lỗi khi finetuning model không tồn tại
   */
  FINETUNING_MODEL_NOT_FOUND: new ErrorCode(
    40099,
    'Fine-tuning model không tồn tại',
    HttpStatus.NOT_FOUND,
  ),

  /**
   * Lỗi khi user provider model không tồn tại
   */
  USER_PROVIDER_MODEL_NOT_FOUND: new ErrorCode(
    40200,
    'User provider model không tồn tại',
    HttpStatus.NOT_FOUND,
  ),

  /**
   * Lỗi khi user không có quyền truy cập provider model
   */
  USER_PROVIDER_MODEL_ACCESS_DENIED: new ErrorCode(
    40201,
    'Không có quyền truy cập provider model',
    HttpStatus.FORBIDDEN,
  ),

  /**
   * Lỗi khi validation model thất bại
   */
  MODEL_VALIDATION_FAILED: new ErrorCode(
    40202,
    'Validation model thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),



  /**
   * Lỗi khi có agent ID trùng lặp trong multi agent
   */
  AGENT_MULTI_AGENT_DUPLICATE: new ErrorCode(
    40206,
    'Không được có agent ID trùng lặp trong Multi Agent',
    HttpStatus.BAD_REQUEST,
  ),

  /**
   * Lỗi khi agent trong multi agent thiếu prompt
   */
  AGENT_MULTI_AGENT_PROMPT_REQUIRED: new ErrorCode(
    40207,
    'Agent trong Multi Agent phải có prompt',
    HttpStatus.BAD_REQUEST,
  ),

  // ===== MULTI AGENTS SYSTEM ERRORS (40100-40109) =====
  /**
   * Lỗi khi không tìm thấy quan hệ giữa các agent
   */
  RELATION_NOT_FOUND: new ErrorCode(
    40100,
    'Không tìm thấy quan hệ giữa các agent',
    HttpStatus.NOT_FOUND,
  ),

  /**
   * Lỗi khi quan hệ giữa các agent đã tồn tại
   */
  RELATION_ALREADY_EXISTS: new ErrorCode(
    40101,
    'Quan hệ giữa các agent đã tồn tại',
    HttpStatus.CONFLICT,
  ),

  /**
   * Lỗi khi tạo quan hệ giữa các agent thất bại
   */
  RELATION_CREATION_FAILED: new ErrorCode(
    40102,
    'Tạo quan hệ giữa các agent thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  /**
   * Lỗi khi cập nhật quan hệ giữa các agent thất bại
   */
  RELATION_UPDATE_FAILED: new ErrorCode(
    40103,
    'Cập nhật quan hệ giữa các agent thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  /**
   * Lỗi khi xóa quan hệ giữa các agent thất bại
   */
  RELATION_DELETE_FAILED: new ErrorCode(
    40104,
    'Xóa quan hệ giữa các agent thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  /**
   * Lỗi khi truy vấn danh sách quan hệ giữa các agent thất bại
   */
  RELATION_QUERY_FAILED: new ErrorCode(
    40105,
    'Lỗi khi truy vấn danh sách quan hệ giữa các agent',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  // ===== FACEBOOK PAGE INTEGRATION ERRORS (40110-40119) =====
  /**
   * Lỗi khi đăng ký webhook cho trang Facebook thất bại
   */
  FACEBOOK_PAGE_SUBSCRIBE_FAILED: new ErrorCode(
    40110,
    'Không thể đăng ký webhook cho trang Facebook',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  /**
   * Lỗi khi hủy đăng ký webhook cho trang Facebook thất bại
   */
  FACEBOOK_PAGE_UNSUBSCRIBE_FAILED: new ErrorCode(
    40111,
    'Không thể hủy đăng ký webhook cho trang Facebook',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  // ===== VALIDATION ERRORS (40120-40139) =====
  /**
   * Lỗi khi dữ liệu profile không hợp lệ
   */
  INVALID_PROFILE_DATA: new ErrorCode(
    40120,
    'Dữ liệu profile không hợp lệ',
    HttpStatus.BAD_REQUEST,
  ),

  /**
   * Lỗi khi dữ liệu conversion không hợp lệ
   */
  INVALID_CONVERSION_DATA: new ErrorCode(
    40121,
    'Dữ liệu conversion không hợp lệ',
    HttpStatus.BAD_REQUEST,
  ),

  /**
   * Lỗi khi dữ liệu strategy không hợp lệ
   */
  INVALID_STRATEGY_DATA: new ErrorCode(
    40122,
    'Dữ liệu strategy không hợp lệ',
    HttpStatus.BAD_REQUEST,
  ),

  /**
   * Lỗi khi dữ liệu multi agent không hợp lệ
   */
  INVALID_MULTI_AGENT_DATA: new ErrorCode(
    40123,
    'Dữ liệu multi agent không hợp lệ',
    HttpStatus.BAD_REQUEST,
  ),

  /**
   * Lỗi khi dữ liệu output messenger không hợp lệ
   */
  INVALID_OUTPUT_MESSENGER_DATA: new ErrorCode(
    40124,
    'Dữ liệu output messenger không hợp lệ',
    HttpStatus.BAD_REQUEST,
  ),

  /**
   * Lỗi khi dữ liệu output website không hợp lệ
   */
  INVALID_OUTPUT_WEBSITE_DATA: new ErrorCode(
    40125,
    'Dữ liệu output website không hợp lệ',
    HttpStatus.BAD_REQUEST,
  ),

  /**
   * Lỗi khi dữ liệu resources không hợp lệ
   */
  INVALID_RESOURCES_DATA: new ErrorCode(
    40126,
    'Dữ liệu resources không hợp lệ',
    HttpStatus.BAD_REQUEST,
  ),

  // ===== MULTI-AGENT ERRORS (40130-40139) =====
  /**
   * Lỗi khi không tìm thấy quan hệ multi-agent
   */
  MULTI_AGENT_NOT_FOUND: new ErrorCode(
    40130,
    'Không tìm thấy quan hệ multi-agent',
    HttpStatus.NOT_FOUND,
  ),

  /**
   * Lỗi khi agent không thể tham chiếu đến chính mình
   */
  MULTI_AGENT_SELF_REFERENCE: new ErrorCode(
    40131,
    'Agent không thể tham chiếu đến chính mình',
    HttpStatus.BAD_REQUEST,
  ),

  /**
   * Lỗi khi tạo quan hệ multi-agent thất bại
   */
  MULTI_AGENT_CREATION_FAILED: new ErrorCode(
    40132,
    'Tạo quan hệ multi-agent thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  /**
   * Lỗi khi xóa quan hệ multi-agent thất bại
   */
  MULTI_AGENT_DELETE_FAILED: new ErrorCode(
    40133,
    'Xóa quan hệ multi-agent thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  // ===== AGENT TOOLS ERRORS (40134-40143) =====
  /**
   * Lỗi khi không tìm thấy tools của agent
   */
  AGENT_TOOLS_NOT_FOUND: new ErrorCode(
    40134,
    'Không tìm thấy tools của agent',
    HttpStatus.NOT_FOUND,
  ),

  /**
   * Lỗi khi thêm tools vào agent thất bại
   */
  AGENT_TOOLS_ADD_FAILED: new ErrorCode(
    40135,
    'Thêm tools vào agent thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  /**
   * Lỗi khi gỡ tools khỏi agent thất bại
   */
  AGENT_TOOLS_REMOVE_FAILED: new ErrorCode(
    40136,
    'Gỡ tools khỏi agent thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  /**
   * Lỗi khi truy vấn tools của agent thất bại
   */
  AGENT_TOOLS_QUERY_FAILED: new ErrorCode(
    40137,
    'Truy vấn tools của agent thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  /**
   * Lỗi khi danh sách tool IDs không hợp lệ
   */
  INVALID_TOOL_IDS: new ErrorCode(
    40138,
    'Danh sách tool IDs không hợp lệ',
    HttpStatus.BAD_REQUEST,
  ),

  /**
   * Lỗi khi không tìm thấy tools được chỉ định
   */
  TOOLS_NOT_FOUND: new ErrorCode(
    40139,
    'Không tìm thấy tools được chỉ định',
    HttpStatus.NOT_FOUND,
  ),

  /**
   * Lỗi khi một số tools đã được gán cho agent
   */
  TOOLS_ALREADY_ASSIGNED: new ErrorCode(
    40140,
    'Một số tools đã được gán cho agent',
    HttpStatus.CONFLICT,
  ),

  /**
   * Lỗi khi tính năng không được enable cho loại agent
   */
  AGENT_FEATURE_NOT_ENABLED: new ErrorCode(
    40141,
    'Tính năng không được hỗ trợ cho loại agent này',
    HttpStatus.FORBIDDEN,
  ),

  // ===== FACEBOOK PAGE INTEGRATION ERRORS (40150-40159) =====
  /**
   * Lỗi khi Facebook Page đã được kết nối với agent khác
   */
  FACEBOOK_PAGE_ALREADY_CONNECTED: new ErrorCode(
    40150,
    'Facebook Page đã được kết nối với agent khác',
    HttpStatus.CONFLICT,
  ),

  /**
   * Lỗi khi không thể kết nối Facebook Page với agent
   */
  FACEBOOK_PAGE_CONNECTION_FAILED: new ErrorCode(
    40151,
    'Không thể kết nối Facebook Page với agent',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  /**
   * Lỗi khi Facebook Page không thuộc về user
   */
  FACEBOOK_PAGE_NOT_OWNED: new ErrorCode(
    40152,
    'Facebook Page không thuộc về người dùng này',
    HttpStatus.FORBIDDEN,
  ),

  /**
   * Lỗi khi không thể gỡ kết nối Facebook Page khỏi agent
   */
  FACEBOOK_PAGE_DISCONNECTION_FAILED: new ErrorCode(
    40153,
    'Không thể gỡ kết nối Facebook Page khỏi agent',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
};
