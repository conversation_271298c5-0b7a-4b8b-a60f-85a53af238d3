# Validators

Thư mục nà<PERSON> chứ<PERSON> các custom validator đ<PERSON><PERSON><PERSON> sử dụng trong toàn bộ ứng dụng.

## International Phone Validator

### Mô tả
Validator để validate số điện thoại theo chuẩn quốc tế sử dụng thư viện `libphonenumber-js`.

### Các Decorator

#### `@IsInternationalPhone()`
Validate số điện thoại quốc tế chuẩn (bao gồm mã quốc gia).

**Sử dụng:**
```typescript
import { IsInternationalPhone } from '@/shared/validators';

class CreateUserDto {
  @IsInternationalPhone()
  @IsNotEmpty()
  phoneNumber: string; // "+84912345678"
}
```

#### `@IsInternationalPhoneWithCountry()`
Validate số điện thoại khi có field `countryCode` riêng biệt trong cùng DTO.

**Sử dụng:**
```typescript
import { IsInternationalPhoneWithCountry } from '@/shared/validators';

class CreateUserDto {
  @IsString()
  countryCode: string; // "+84"

  @IsInternationalPhoneWithCountry()
  @IsNotEmpty()
  phoneNumber: string; // "912345678"
}
```

### Utility Functions

#### `formatInternationalPhone(phone, defaultCountry?)`
Format số điện thoại về định dạng quốc tế.

```typescript
import { formatInternationalPhone } from '@/shared/validators';

const formatted = formatInternationalPhone('+84912345678');
// Kết quả: "+84 91 234 5678"
```

#### `getPhoneInfo(phone, defaultCountry?)`
Lấy thông tin chi tiết về số điện thoại.

```typescript
import { getPhoneInfo } from '@/shared/validators';

const info = getPhoneInfo('+84912345678');
// Kết quả:
// {
//   isValid: true,
//   international: "+84 91 234 5678",
//   national: "************",
//   e164: "+84912345678",
//   country: "VN",
//   countryCallingCode: "84",
//   type: "MOBILE"
// }
```

### Ví dụ sử dụng trong DTO

```typescript
import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString } from 'class-validator';
import { IsInternationalPhone } from '@/shared/validators';

export class RegisterDto {
  @ApiProperty({
    description: 'Số điện thoại của người dùng (định dạng quốc tế)',
    example: '+84912345678',
    required: true,
  })
  @IsString({ message: 'Số điện thoại phải là chuỗi' })
  @IsNotEmpty({ message: 'Số điện thoại không được để trống' })
  @IsInternationalPhone({ 
    message: 'Số điện thoại không hợp lệ, phải là số điện thoại quốc tế hợp lệ (ví dụ: +84912345678)' 
  })
  phoneNumber: string;
}
```

### Các định dạng số điện thoại được hỗ trợ

- **Việt Nam**: +84912345678, +84 91 234 5678
- **Mỹ**: +1234567890, ****** 567 890
- **Anh**: +447911123456, +44 7911 123456
- **Trung Quốc**: +8613800138000, +86 138 0013 8000
- **Nhật Bản**: +819012345678, +81 90 1234 5678
- **Hàn Quốc**: +821012345678, +82 10 1234 5678

### Lưu ý

1. **Luôn sử dụng định dạng quốc tế**: Số điện thoại phải bắt đầu bằng dấu `+` và mã quốc gia.
2. **Thay thế các validator cũ**: Sử dụng `@IsInternationalPhone()` thay vì `@IsPhoneNumber()` hoặc `@Matches()` với regex.
3. **Tương thích với libphonenumber-js**: Validator này sử dụng thư viện chuẩn để đảm bảo tính chính xác cao.
4. **Hỗ trợ tất cả quốc gia**: Validator hỗ trợ validate số điện thoại của tất cả các quốc gia trên thế giới.

### Migration từ validator cũ

**Trước:**
```typescript
@IsPhoneNumber(undefined, { message: 'Số điện thoại không hợp lệ' })
// hoặc
@Matches(/^[\+]?[1-9][\d]{0,15}$/, {
  message: 'Số điện thoại không hợp lệ, phải là số điện thoại quốc tế hợp lệ',
})
```

**Sau:**
```typescript
@IsInternationalPhone({ 
  message: 'Số điện thoại không hợp lệ, phải là số điện thoại quốc tế hợp lệ (ví dụ: +84912345678)' 
})
```
