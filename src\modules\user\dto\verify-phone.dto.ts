import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNotEmpty, Length, Matches } from 'class-validator';

/**
 * DTO cho yêu cầu gửi OTP xác thực số điện thoại
 */
export class SendPhoneVerificationOtpDto {
  @ApiProperty({
    description: 'Số điện thoại cần xác thực',
    example: '0987654321',
  })
  @IsString({ message: 'Số điện thoại phải là chuỗi' })
  @IsNotEmpty({ message: 'Số điện thoại không được để trống' })
  @Matches(/^[0-9+\-\s()]+$/, { message: 'Số điện thoại không hợp lệ' })
  phoneNumber: string;
}

/**
 * DTO cho yêu cầu xác thực OTP số điện thoại
 */
export class VerifyPhoneOtpDto {
  @ApiProperty({
    description: 'S<PERSON> điện thoại đã gửi OTP',
    example: '0987654321',
  })
  @IsString({ message: 'Số điện thoại phải là chuỗi' })
  @IsNotEmpty({ message: 'Số điện thoại không được để trống' })
  @Matches(/^[0-9+\-\s()]+$/, { message: 'Số điện thoại không hợp lệ' })
  phoneNumber: string;

  @ApiProperty({
    description: 'Mã OTP 6 số',
    example: '123456',
  })
  @IsString({ message: 'OTP phải là chuỗi' })
  @IsNotEmpty({ message: 'OTP không được để trống' })
  @Length(6, 6, { message: 'OTP phải có đúng 6 ký tự' })
  @Matches(/^\d{6}$/, { message: 'OTP phải là 6 chữ số' })
  otp: string;
}

/**
 * DTO cho response gửi OTP xác thực số điện thoại
 */
export class SendPhoneVerificationOtpResponseDto {
  @ApiProperty({
    description: 'Thông báo kết quả',
    example: 'Đã gửi mã OTP đến số điện thoại ***7654321',
  })
  message: string;

  @ApiProperty({
    description: 'Số điện thoại đã che một phần',
    example: '***7654321',
  })
  maskedPhoneNumber: string;

  @ApiProperty({
    description: 'Thời gian hết hạn OTP (timestamp)',
    example: 1640995500000,
  })
  expiresAt: number;

  @ApiProperty({
    description: 'Thời gian chờ trước khi có thể gửi lại OTP (giây)',
    example: 60,
  })
  cooldownSeconds: number;
}

/**
 * DTO cho response xác thực OTP số điện thoại
 */
export class VerifyPhoneOtpResponseDto {
  @ApiProperty({
    description: 'Thông báo kết quả',
    example: 'Xác thực số điện thoại thành công',
  })
  message: string;

  @ApiProperty({
    description: 'Trạng thái xác thực số điện thoại',
    example: true,
  })
  isVerifyPhone: boolean;

  @ApiProperty({
    description: 'Số điện thoại đã xác thực',
    example: '0987654321',
  })
  phoneNumber: string;
}
