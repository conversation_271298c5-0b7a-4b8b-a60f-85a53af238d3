import { Injectable, Logger } from '@nestjs/common';
import { AuthVerificationLogRepository } from '../../repositories/auth-verification-log.repository';
import { AuthVerificationLogQueryDto, AuthVerificationLogResponseDto } from '../../dto/auth-verification-log.dto';
import { PaginatedResult, PaginationMeta } from '@common/response';
import { AuthVerificationLog } from '../../entities/auth-verification-log.entity';

/**
 * Service cho quản lý log xác thực
 */
@Injectable()
export class AuthVerificationLogService {
  private readonly logger = new Logger(AuthVerificationLogService.name);

  constructor(
    private readonly authVerificationLogRepository: AuthVerificationLogRepository,
  ) {}

  /**
   * Lấy danh sách log xác thực của người dùng hiện tại
   * @param userId ID của người dùng
   * @param queryDto Query parameters
   * @returns Danh sách log xác thực với phân trang
   */
  async getAuthVerificationLogs(
    userId: number,
    queryDto: AuthVerificationLogQueryDto,
  ): Promise<PaginatedResult<AuthVerificationLogResponseDto>> {
    try {
      this.logger.debug(`Lấy danh sách log xác thực cho userId: ${userId}`);

      const {
        authMethod,
        status,
        ipAddress,
        fromDate,
        toDate,
        page = 1,
        limit = 10,
      } = queryDto;

      // Tính toán offset
      const offset = (page - 1) * limit;

      // Xây dựng query builder
      const queryBuilder = this.authVerificationLogRepository['repository']
        .createQueryBuilder('log')
        .where('log.userId = :userId', { userId });

      // Thêm các điều kiện filter
      if (authMethod) {
        queryBuilder.andWhere('log.authMethod = :authMethod', { authMethod });
      }

      if (status) {
        queryBuilder.andWhere('log.status = :status', { status });
      }

      if (ipAddress) {
        queryBuilder.andWhere('log.ipAddress LIKE :ipAddress', { 
          ipAddress: `%${ipAddress}%` 
        });
      }

      if (fromDate) {
        queryBuilder.andWhere('log.createdAt >= :fromDate', { fromDate });
      }

      if (toDate) {
        queryBuilder.andWhere('log.createdAt <= :toDate', { toDate });
      }

      // Sắp xếp theo thời gian tạo mới nhất
      queryBuilder.orderBy('log.createdAt', 'DESC');

      // Lấy tổng số bản ghi
      const total = await queryBuilder.getCount();

      // Áp dụng phân trang
      queryBuilder.skip(offset).take(limit);

      // Lấy dữ liệu
      const logs = await queryBuilder.getMany();

      // Chuyển đổi sang DTO
      const items = logs.map(log => this.mapToResponseDto(log));

      // Tính toán thông tin phân trang
      const totalPages = Math.ceil(total / limit);
      const hasNext = page < totalPages;
      const hasPrev = page > 1;

      return {
        items,
        meta: {
          totalItems: total,
          itemCount: items.length,
          itemsPerPage: limit,
          totalPages,
          currentPage: page,
        },
      };
    } catch (error) {
      this.logger.error(
        `Lỗi khi lấy danh sách log xác thực cho userId ${userId}: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Lấy chi tiết log xác thực theo ID
   * @param userId ID của người dùng
   * @param logId ID của log
   * @returns Chi tiết log xác thực
   */
  async getAuthVerificationLogById(
    userId: number,
    logId: number,
  ): Promise<AuthVerificationLogResponseDto | null> {
    try {
      this.logger.debug(`Lấy chi tiết log xác thực ${logId} cho userId: ${userId}`);

      const log = await this.authVerificationLogRepository['repository']
        .createQueryBuilder('log')
        .where('log.id = :logId', { logId })
        .andWhere('log.userId = :userId', { userId })
        .getOne();

      if (!log) {
        return null;
      }

      return this.mapToResponseDto(log);
    } catch (error) {
      this.logger.error(
        `Lỗi khi lấy chi tiết log xác thực ${logId} cho userId ${userId}: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Lấy thống kê log xác thực của người dùng
   * @param userId ID của người dùng
   * @param fromDate Từ ngày (timestamp)
   * @param toDate Đến ngày (timestamp)
   * @returns Thống kê log xác thực
   */
  async getAuthVerificationStats(
    userId: number,
    fromDate?: number,
    toDate?: number,
  ): Promise<{
    totalAttempts: number;
    successfulAttempts: number;
    failedAttempts: number;
    successRate: number;
    methodStats: Array<{
      method: string;
      count: number;
      successCount: number;
    }>;
  }> {
    try {
      this.logger.debug(`Lấy thống kê log xác thực cho userId: ${userId}`);

      const queryBuilder = this.authVerificationLogRepository['repository']
        .createQueryBuilder('log')
        .where('log.userId = :userId', { userId });

      if (fromDate) {
        queryBuilder.andWhere('log.createdAt >= :fromDate', { fromDate });
      }

      if (toDate) {
        queryBuilder.andWhere('log.createdAt <= :toDate', { toDate });
      }

      const logs = await queryBuilder.getMany();

      const totalAttempts = logs.length;
      const successfulAttempts = logs.filter(log => log.status === 'SUCCESS').length;
      const failedAttempts = totalAttempts - successfulAttempts;
      const successRate = totalAttempts > 0 ? (successfulAttempts / totalAttempts) * 100 : 0;

      // Thống kê theo phương thức
      const methodStatsMap = new Map();
      logs.forEach(log => {
        const method = log.authMethod;
        if (!methodStatsMap.has(method)) {
          methodStatsMap.set(method, { count: 0, successCount: 0 });
        }
        const stats = methodStatsMap.get(method);
        stats.count++;
        if (log.status === 'SUCCESS') {
          stats.successCount++;
        }
      });

      const methodStats = Array.from(methodStatsMap.entries()).map(([method, stats]) => ({
        method,
        count: stats.count,
        successCount: stats.successCount,
      }));

      return {
        totalAttempts,
        successfulAttempts,
        failedAttempts,
        successRate: Math.round(successRate * 100) / 100,
        methodStats,
      };
    } catch (error) {
      this.logger.error(
        `Lỗi khi lấy thống kê log xác thực cho userId ${userId}: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Chuyển đổi entity sang response DTO
   * @param log Entity log xác thực
   * @returns Response DTO
   */
  private mapToResponseDto(log: AuthVerificationLog): AuthVerificationLogResponseDto {
    return {
      id: log.id,
      userId: log.userId,
      authMethod: log.authMethod,
      status: log.status,
      ipAddress: log.ipAddress,
      userAgent: log.userAgent,
      codeSentAt: log.codeSentAt,
      verifiedAt: log.verifiedAt,
      attemptCount: log.attemptCount,
      createdAt: log.createdAt,
    };
  }
}
